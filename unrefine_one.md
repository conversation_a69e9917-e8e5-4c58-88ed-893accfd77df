 Workflow Chart Description:
Main flows:
•Admin Login → Institute Dashboard
•Student Referral & Registration via •QR/Link
•Student Document Upload & Verification
•Document Submission to Japanese •College
•Application Progress Tracking
•Chat Communication
•Referral Reward Tracking & Payout
•Notifications & Posts
•Online course scheduling
🔐 1. Authentication & Access Control
•Admin login via email/password
•Each admin linked to one institute only
•Strict access control — no cross-institute visibility
•Support for multiple admin roles per institute (Main Admin, Counselor, Accountant)
🏫 2. Institute Profile Management
•View and edit institute details:
•Institute Name
•Address
•Phone Number
•Auto-generated student referral link
•Auto-generated QR code based on referral link
🎓 3. Course Management
•Central Course Master Table:
Create/edit course templates (Name, Fee)
•Assign courses to students via selection from master list
🎓 4. Student Management
•View students registered via institute referral link or QR code
•Student details:
•Full Name
•Phone Number
•Address
•Assigned Course
•Application Progress Stage
•Last Updated Timestamp
•Students auto-linked to institute on registration
📄 5. Document Handling & Smart Checklist
•Students upload required documents:
Passport (auto-accepted)
Academic Certificates (auto-accepted)
National ID (auto-accepted)
Financial Proof (manual verification required)
Personal Photo (manual verification required)
•Optional additional documents
•Document checklist per student with status:
Uploaded / Missing / Pending Verification
•Admin can:
View documents
Verify / Reject documents
Leave comments
Request missing uploads (triggers student notification)
Document verification history log (timestamps, admin ID)
•Filters:
Students with missing documents
Documents pending verification
•After all documents are verified:
Institute can generate and share a secure cloud link or document package of all verified
documents and application data with the Japanese college
•Track submission status (Sent, Confirmed Received)
•Option to download compiled ZIP or PDF bundle for offline use
📈 6. Application Progress Tracker
•Manual 5-step status update:
Documents Submitted
Documents Verified
College Application Sent
Documents Submitted to College (new step)
Final Process Completed
•Dashboard overview with progress filters
💬 7. Chat System
•One-to-one real-time chat (admin ↔ student)
•Text and file exchange
•Notifications for new and unread messages
•Secure, private conversation per student and assigned admin
💰 8. Referral & Reward Tracking
•View total referred students
•View subscribed students
10% commission earnings from referrals
Withdrawable balance
•Manual payout requests via eSewa, Khalti, Bank Transfer
🔔 9. Notifications
Alerts for:
•New student referral
•Document upload
•Document verification requests
•Document submission to college (sent/confirmed)
•New chat messages
•Admin configurable delivery: Email, In-app, or both
🧾 10. Student Admission & Documentation Fee Tracker
•Assign courses to students from master list
•Input and track fees:
•Total Course Fee and Documentation fee
•Amount Received
•Auto-calculated Remaining Fee
•Optional payment notes
•Filter students by payment status: Fully Paid / Partially Paid / Unpaid
📋 11. Data Export
Export CSV reports for:
•Student lists with statuses
•Document verification summary
•Fee collection reports
•Referral earnings
•Document submission to college status
📱 12. Responsive Web Admin Panel
•Fully mobile-friendly and desktop-compatible UI
🕒 13. Document Verification History
•Logs of all document status changes with timestamps and admin IDs
🛠 14. Multi-Role Admin Support
Define and manage multiple roles within an institute with different access rights
Main admin can:
•Add staff (name, email, select role: •Counselor, Accountant, Teacher)
•Send invite (auto-email with login link)
•View all staff in a list (name, email, role, status)
•Remove staff with a single click
🔄 15. Document Upload Request System
“Request Upload” button for missing documents that triggers student notifications
📝 16. Institute Announcements & Posts
•Admins can create text/image posts for students
•Posts visible in student app/web in a feed
•Likes/comments on posts
•Post scheduling
•Notifications on new posts (optional)
•Post management in admin panel (create/edit/delete)
🔔 17. Automated Reminder System
•Automated alerts for students on missing documents, pending fees, or deadlines
🎥 18. Online Course Scheduling (Google Meet)
•Admin or Teacher can schedule online classes
•Add Class Title
•Select Date & Time
•Paste Google Meet link
•Select Course (e.g., N5, N4) or specific students
•Optional description or notes
•Students can view upcoming classes in their app:
~Course name, time, and “Join Now” ~Google Meet button
~Reminders before class starts (via notification)
~Admin Panel includes:
~Upcoming and past class list
~Option to edit or delete classes
~Filters by course   📱 Student App 
🔐 1. Login & Profile
Login via phone number or email
Auto-linked to institute via QR or referral link
View/edit basic profile (name, phone, address)

🎓 2. Course Overview
View assigned course (e.g., N5, N4)
View course fee, paid amount, remaining fee
See notes from institute (e.g., payment method)

📚 3. Learning Section
📖 A. Alphabets (Free)
Learn Hiragana and Katakana
Reading + Writing stroke order animations
Sound playback for pronunciation
🃏 B. Basic Vocabulary Flashcards (Free)
Illustrated word cards with:
Japanese word
Romaji
English & Nepali translations
Sound pronunciation
❓ C. Interactive Quizzes (Free)
Multiple-choice quizzes based on vocabulary and alphabets
Immediate feedback & score tracking
🧠 D. Model Questions (Subscription)
Practice N5/N4-level grammar, vocabulary, reading
Structured like real exam format
Available only to subscribed users

📄 4. Document Upload
Upload required documents:
Passport
Academic Certificates
National ID
Financial Proof
Photo
Optional additional documents
Real-time checklist (Uploaded / Verified / Missing / Rejected)
Admin comments on document status
Upload alert notifications

📈 5. Application Progress Tracker
View 5-stage application progress:
Documents Submitted
Documents Verified
College Application Sent
Documents Submitted to College
Final Process Completed

💬 6. Chat with Institute
One-to-one chat with institute admin/staff
Send/receive messages and files
Get reply notifications and unread badges

🔔 7. Notifications
Alerts for:
Document requests or rejection
Progress updates
Chat messages
Class reminders
New institute announcements

🎥 8. Online Classes (Google Meet)
View scheduled online classes
Class details: title, date, time, description
"Join Now" button for Google Meet
Class reminder alerts

🗞️ 9. Institute Announcements & Posts
View institute announcements (text/image posts)
Optional: like or comment if enabled
Notification on new posts

🧾 10. Fee Status
View total course fee
See paid amount and remaining balance
View payment notes from admin
Payment status: Paid / Partially Paid / Unpaid

🚫 11. No Referral from Students
Students cannot refer others
Referral is institute-only  🧑‍💼 **Super Admin Panel**
🏠 **Main Dashboard (Overview)**

* Total Institutes
* Total Students
* Total Subscriptions
* Total Payouts Requested
* Quick Stats:

  * Pending Documents
  * New Referrals Today
  * Subscription Earnings (Today / This Month)
* Top 5 Institutes (by referrals/subscriptions)

---

### 🏫 **Institutes Module**

* **Institute List View:**

  * Institute Name
  * Phone
  * Total Students
  * Referral Link
  * Status (Active / Disabled)
  * Action: View / Edit / Disable
* **Institute Detail Page:**

  * Profile Info
  * Referred Student List
  * Admin & Staff List
  * Payout History
  * Posts Made
  * Learning Usage Report

---

### 👨‍🏫 **Admin & Staff Management**

* Add new admin/staff
* Filter by role (Main, Counselor, Accountant, Teacher)
* View last login, status, and actions
* Reset password / Disable user

---

### 🎓 **Student Management**

* Full list with filters:

  * By Institute, Course, Progress Stage, Fee Status
* Actions:

  * View Profile
  * View Documents
  * View Learning Progress
  * Reset Login / Disable

---

### 📁 **Documents & Verification**

* **Global Document Tracker**

  * Filter: Missing / Pending / Verified / Rejected
  * View by Institute
  * View logs of verification (admin ID, timestamp)
* Toggle for auto-verification rule (for specific document types)

---

### 💰 **Payments & Subscriptions**

* **Student Subscriptions:**

  * User name, product (e.g., Model Questions), date, amount
  * Status (Active / Expired)
* **Referral Payouts:**

  * Institute name, earnings, payout method
  * Requested / Approved / Paid
  * Export CSV

---

### 📚 **Learning Content Manager**

* **Alphabet & Flashcards**

  * Upload/edit characters, romaji, images, audio
* **Vocabulary Cards**

  * Word, Image, Sound, Translation
* **Quiz Builder**

  * Questions, Options, Answer
  * Tag as Free / Premium
* **Model Questions**

  * Sectioned N5/N4 mock exams
  * Mark subscription required

---

### 📣 **Post Moderation**

* View all institute posts
* Status: Published / Scheduled / Draft
* Actions: Approve / Edit / Delete
* Metrics: Views, Likes, Comments

---

### 🎥 **Online Class Monitor**

* Class List

  * Title, Date, Time, Course
  * Institute Name
  * Google Meet Link
  * Status: Upcoming / Past
* Optional: Manual attendance record (future)

---

### 📈 **Analytics & Insights**

* Filter by Date Range
* Graphs:

  * Student Growth
  * Subscriptions
  * Document Submission Trends
  * Referral Performance
* Export Graphs as Image/PDF

---

### 🧾 **Data Export**

* Select table:

  * Students
  * Documents
  * Fees
  * Subscriptions
  * Referrals
* Format: CSV / XLSX
* Optional filters by date/institute

---

### 🌐 **Language Settings**

* Add/Edit translations for English / Nepali
* Apply to:

  * System labels
  * Quiz content
  * Vocabulary

---

### 🧪 **Test Environment**

* Create Test Institute / Student
* View all test data separately
* Reset sandbox with 1 click

---

### ⚙️ **System Settings**

* Manage:

  * Default document types
  * Notification toggles
  * Feature toggles (e.g., enable referrals, model questions)
  * Payment gateway configs
  * Auto-verification rules

---

### 🔒 **Security Panel**

* View all super admin login logs
* Audit trail of document approvals, user actions
* Block users/IPs
* Lock entire institute (if needed) 
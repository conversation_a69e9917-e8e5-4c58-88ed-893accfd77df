<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Institute;
use App\Models\Student;
use App\Models\ReferralCode;
use App\Models\ReferralEarning;
use App\Models\ReferralTracking;
use Laravel\Sanctum\Sanctum;

class ReferralControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $institute;
    protected $admin;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create institute
        $this->institute = Institute::factory()->create([
            'name' => 'Test Institute',
            'commission_rate' => 10.00,
        ]);

        // Create admin user
        $this->admin = User::factory()->create([
            'institute_id' => $this->institute->id,
            'role' => 'admin',
        ]);

        // Create student
        $this->student = Student::factory()->create([
            'institute_id' => $this->institute->id,
        ]);
    }

    public function test_can_get_referral_overview()
    {
        Sanctum::actingAs($this->admin);

        // Create some test data
        ReferralEarning::factory()->create([
            'institute_id' => $this->institute->id,
            'student_id' => $this->student->id,
            'commission_amount' => 100.00,
            'status' => 'approved',
        ]);

        $response = $this->getJson('/api/v1/admin/referrals/overview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview' => [
                            'total_students',
                            'subscribed_students',
                            'total_earnings',
                            'pending_earnings',
                            'withdrawable_balance',
                            'conversion_rate',
                        ],
                        'recent_referrals',
                        'analytics',
                    ],
                    'message'
                ]);
    }

    public function test_can_create_referral_code()
    {
        Sanctum::actingAs($this->admin);

        $data = [
            'type' => 'campaign',
            'description' => 'Test campaign code',
            'usage_limit' => 100,
            'commission_rate' => 15.00,
        ];

        $response = $this->postJson('/api/v1/admin/referrals/codes', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'code',
                        'type',
                        'description',
                        'usage_limit',
                        'commission_rate',
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('referral_codes', [
            'institute_id' => $this->institute->id,
            'type' => 'campaign',
            'description' => 'Test campaign code',
            'usage_limit' => 100,
            'commission_rate' => 15.00,
        ]);
    }

    public function test_can_get_referral_codes()
    {
        Sanctum::actingAs($this->admin);

        // Create test referral codes
        ReferralCode::factory()->count(3)->create([
            'institute_id' => $this->institute->id,
        ]);

        $response = $this->getJson('/api/v1/admin/referrals/codes');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'code',
                                'type',
                                'status',
                                'usage_count',
                                'usage_limit',
                            ]
                        ]
                    ],
                    'message'
                ]);
    }

    public function test_can_update_referral_code()
    {
        Sanctum::actingAs($this->admin);

        $referralCode = ReferralCode::factory()->create([
            'institute_id' => $this->institute->id,
            'status' => 'active',
        ]);

        $updateData = [
            'status' => 'inactive',
            'description' => 'Updated description',
        ];

        $response = $this->putJson("/api/v1/admin/referrals/codes/{$referralCode->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('referral_codes', [
            'id' => $referralCode->id,
            'status' => 'inactive',
            'description' => 'Updated description',
        ]);
    }

    public function test_can_get_referral_earnings()
    {
        Sanctum::actingAs($this->admin);

        // Create test earnings
        ReferralEarning::factory()->count(5)->create([
            'institute_id' => $this->institute->id,
            'student_id' => $this->student->id,
        ]);

        $response = $this->getJson('/api/v1/admin/referrals/earnings');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'commission_amount',
                                'status',
                                'earned_at',
                                'student',
                            ]
                        ]
                    ],
                    'message'
                ]);
    }

    public function test_can_request_payout()
    {
        Sanctum::actingAs($this->admin);

        // Create approved earnings
        $earnings = ReferralEarning::factory()->count(2)->create([
            'institute_id' => $this->institute->id,
            'student_id' => $this->student->id,
            'status' => 'approved',
            'commission_amount' => 50.00,
        ]);

        $payoutData = [
            'payout_method' => 'esewa',
            'payout_details' => '9841234567',
            'amount' => 100.00,
            'earning_ids' => $earnings->pluck('id')->toArray(),
        ];

        $response = $this->postJson('/api/v1/admin/referrals/payout', $payoutData);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'message',
                        'total_amount',
                        'earnings_count',
                    ],
                    'message'
                ]);

        // Verify earnings are marked as paid
        foreach ($earnings as $earning) {
            $this->assertDatabaseHas('referral_earnings', [
                'id' => $earning->id,
                'status' => 'paid',
                'payout_method' => 'esewa',
                'payout_details' => '9841234567',
            ]);
        }
    }

    public function test_cannot_access_other_institute_data()
    {
        // Create another institute and admin
        $otherInstitute = Institute::factory()->create();
        $otherAdmin = User::factory()->create([
            'institute_id' => $otherInstitute->id,
            'role' => 'admin',
        ]);

        Sanctum::actingAs($otherAdmin);

        // Try to access our institute's referral code
        $referralCode = ReferralCode::factory()->create([
            'institute_id' => $this->institute->id,
        ]);

        $response = $this->putJson("/api/v1/admin/referrals/codes/{$referralCode->id}", [
            'status' => 'inactive',
        ]);

        $response->assertStatus(404);
    }

    public function test_referral_code_validation()
    {
        Sanctum::actingAs($this->admin);

        // Test invalid type
        $response = $this->postJson('/api/v1/admin/referrals/codes', [
            'type' => 'invalid_type',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['type']);

        // Test invalid commission rate
        $response = $this->postJson('/api/v1/admin/referrals/codes', [
            'type' => 'general',
            'commission_rate' => 150, // Over 100%
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['commission_rate']);
    }
}

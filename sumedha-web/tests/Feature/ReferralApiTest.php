<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Institute;
use App\Models\Student;
use App\Models\ReferralCode;
use App\Models\ReferralEarning;
use Lara<PERSON>\Sanctum\Sanctum;

class ReferralApiTest extends TestCase
{
    use RefreshDatabase;

    protected $institute;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations
        $this->artisan('migrate:fresh');

        // Create institute
        $this->institute = Institute::create([
            'name' => 'Test Institute',
            'address' => '123 Test Street',
            'phone' => '1234567890',
            'email' => '<EMAIL>',
            'referral_link' => 'test-referral-link',
            'commission_rate' => 10.00,
        ]);

        // Create admin user
        $this->admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'institute_id' => $this->institute->id,
            'role' => 'admin',
            'user_type' => 'admin',
            'status' => 'active',
        ]);

        // Load the institute relationship
        $this->admin->load('institute');
    }

    public function test_can_access_referral_overview()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/referrals/overview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview' => [
                            'total_students',
                            'subscribed_students',
                            'total_earnings',
                            'pending_earnings',
                            'withdrawable_balance',
                            'conversion_rate',
                        ],
                        'recent_referrals',
                        'analytics',
                    ],
                    'message'
                ]);
    }

    public function test_can_create_referral_code()
    {
        Sanctum::actingAs($this->admin);

        $data = [
            'type' => 'campaign',
            'description' => 'Test campaign code',
            'usage_limit' => 100,
            'commission_rate' => 15.00,
        ];

        $response = $this->postJson('/api/v1/admin/referrals/codes', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'code',
                        'type',
                        'description',
                        'usage_limit',
                        'commission_rate',
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('referral_codes', [
            'institute_id' => $this->institute->id,
            'type' => 'campaign',
            'description' => 'Test campaign code',
            'usage_limit' => 100,
            'commission_rate' => 15.00,
        ]);
    }

    public function test_referral_code_validation()
    {
        Sanctum::actingAs($this->admin);

        // Test invalid type
        $response = $this->postJson('/api/v1/admin/referrals/codes', [
            'type' => 'invalid_type',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['type']);

        // Test invalid commission rate
        $response = $this->postJson('/api/v1/admin/referrals/codes', [
            'type' => 'general',
            'commission_rate' => 150, // Over 100%
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['commission_rate']);
    }

    public function test_can_get_referral_codes()
    {
        Sanctum::actingAs($this->admin);

        // Create test referral code
        ReferralCode::create([
            'institute_id' => $this->institute->id,
            'code' => 'TEST123',
            'type' => 'general',
            'status' => 'active',
            'created_by' => $this->admin->id,
        ]);

        $response = $this->getJson('/api/v1/admin/referrals/codes');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data' => [
                            '*' => [
                                'id',
                                'code',
                                'type',
                                'status',
                                'usage_count',
                            ]
                        ]
                    ],
                    'message'
                ]);
    }

    public function test_unauthorized_access_denied()
    {
        // Test without authentication
        $response = $this->getJson('/api/v1/admin/referrals/overview');
        $response->assertStatus(401);

        // Test with different institute user
        $otherInstitute = Institute::create([
            'name' => 'Other Institute',
            'address' => '456 Other Street',
            'phone' => '0987654321',
            'email' => '<EMAIL>',
            'referral_link' => 'other-referral-link',
            'commission_rate' => 12.00,
        ]);

        $otherAdmin = User::create([
            'name' => 'Other Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'institute_id' => $otherInstitute->id,
            'role' => 'admin',
            'user_type' => 'admin',
        ]);

        Sanctum::actingAs($otherAdmin);

        // Create referral code for first institute
        $referralCode = ReferralCode::create([
            'institute_id' => $this->institute->id,
            'code' => 'PRIVATE123',
            'type' => 'general',
            'status' => 'active',
            'created_by' => $this->admin->id,
        ]);

        // Try to update other institute's referral code
        $response = $this->putJson("/api/v1/admin/referrals/codes/{$referralCode->id}", [
            'status' => 'inactive',
        ]);

        $response->assertStatus(403);
    }

    public function test_reward_points_overview()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/rewards/overview');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview' => [
                            'total_points_awarded',
                            'total_points_redeemed',
                            'active_points',
                            'expired_points',
                        ],
                        'points_by_type',
                        'top_students',
                    ],
                    'message'
                ]);
    }

    public function test_can_award_points()
    {
        Sanctum::actingAs($this->admin);

        // Create a student
        $student = Student::create([
            'institute_id' => $this->institute->id,
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Student Street',
            'password' => bcrypt('password'),
            'status' => 'active',
        ]);

        $data = [
            'student_id' => $student->id,
            'points' => 100,
            'type' => 'bonus',
            'description' => 'Test bonus points',
        ];

        $response = $this->postJson('/api/v1/admin/rewards/award', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'points',
                        'type',
                        'description',
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('reward_points', [
            'institute_id' => $this->institute->id,
            'student_id' => $student->id,
            'points' => 100,
            'type' => 'bonus',
            'description' => 'Test bonus points',
        ]);
    }
}

<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\User;
use App\Models\Institute;
use App\Models\Student;
use App\Models\NotificationTemplate;
use App\Models\NotificationLog;
use App\Models\NotificationPreference;
use App\Services\NotificationService;
use Laravel\Sanctum\Sanctum;

class NotificationApiTest extends TestCase
{
    use RefreshDatabase;

    protected $institute;
    protected $admin;
    protected $student;
    protected $notificationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Run migrations
        $this->artisan('migrate:fresh');

        // Create institute
        $this->institute = Institute::create([
            'name' => 'Test Institute',
            'address' => '123 Test Street',
            'phone' => '1234567890',
            'email' => '<EMAIL>',
            'referral_link' => 'test-referral-link',
            'commission_rate' => 10.00,
        ]);

        // Create admin user
        $this->admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'institute_id' => $this->institute->id,
            'role' => 'admin',
            'user_type' => 'admin',
            'status' => 'active',
        ]);

        // Create student
        $this->student = Student::create([
            'institute_id' => $this->institute->id,
            'name' => 'Test Student',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'address' => '123 Student Street',
            'password' => bcrypt('password'),
            'status' => 'active',
        ]);

        $this->notificationService = new NotificationService();
    }

    public function test_can_get_notifications()
    {
        Sanctum::actingAs($this->admin);

        // Create some test notifications
        NotificationLog::createLog('admin', $this->admin->id, 'in_app', 'test_event', 'Test Subject', 'Test Content');
        NotificationLog::createLog('admin', $this->admin->id, 'in_app', 'test_event', 'Test Subject 2', 'Test Content 2');

        $response = $this->getJson('/api/v1/admin/notifications/');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'notifications',
                        'unread_count',
                        'total_count',
                    ],
                    'message'
                ]);
    }

    public function test_can_mark_notification_as_read()
    {
        Sanctum::actingAs($this->admin);

        $notification = NotificationLog::createLog('admin', $this->admin->id, 'in_app', 'test_event', 'Test Subject', 'Test Content');

        $response = $this->putJson("/api/v1/admin/notifications/{$notification->id}/read");

        $response->assertStatus(200);

        $notification->refresh();
        $this->assertNotNull($notification->read_at);
    }

    public function test_can_mark_all_notifications_as_read()
    {
        Sanctum::actingAs($this->admin);

        // Create multiple notifications
        NotificationLog::createLog('admin', $this->admin->id, 'in_app', 'test_event', 'Test Subject 1', 'Test Content 1');
        NotificationLog::createLog('admin', $this->admin->id, 'in_app', 'test_event', 'Test Subject 2', 'Test Content 2');

        $response = $this->putJson('/api/v1/admin/notifications/mark-all-read');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'marked_count',
                    ],
                    'message'
                ]);

        // Verify all notifications are marked as read
        $unreadCount = NotificationLog::forRecipient('admin', $this->admin->id)->unread()->count();
        $this->assertEquals(0, $unreadCount);
    }

    public function test_can_send_custom_notification()
    {
        Sanctum::actingAs($this->admin);

        $data = [
            'recipient_type' => 'student',
            'recipient_ids' => [$this->student->id],
            'subject' => 'Test Custom Notification',
            'content' => 'This is a test custom notification content.',
            'channels' => ['in_app'],
        ];

        $response = $this->postJson('/api/v1/admin/notifications/send', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'sent_to',
                        'count',
                    ],
                    'message'
                ]);

        // Verify notification was created
        $this->assertDatabaseHas('notification_logs', [
            'recipient_type' => 'student',
            'recipient_id' => $this->student->id,
            'subject' => 'Test Custom Notification',
            'content' => 'This is a test custom notification content.',
        ]);
    }

    public function test_can_get_notification_templates()
    {
        Sanctum::actingAs($this->admin);

        // Create a test template
        NotificationTemplate::create([
            'name' => 'Test Template',
            'type' => 'email',
            'event' => 'test_event',
            'subject' => 'Test Subject',
            'content' => 'Test Content',
            'variables' => ['test_var'],
            'created_by' => $this->admin->id,
        ]);

        $response = $this->getJson('/api/v1/admin/notifications/templates');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'type',
                            'event',
                            'subject',
                            'content',
                        ]
                    ],
                    'message'
                ]);
    }

    public function test_can_create_notification_template()
    {
        Sanctum::actingAs($this->admin);

        $data = [
            'name' => 'New Test Template',
            'type' => 'email',
            'event' => 'new_test_event',
            'subject' => 'New Test Subject',
            'content' => 'New test content with {{variable}}',
            'variables' => ['variable'],
        ];

        $response = $this->postJson('/api/v1/admin/notifications/templates', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'type',
                        'event',
                        'subject',
                        'content',
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('notification_templates', [
            'name' => 'New Test Template',
            'type' => 'email',
            'event' => 'new_test_event',
            'created_by' => $this->admin->id,
        ]);
    }

    public function test_can_update_notification_template()
    {
        Sanctum::actingAs($this->admin);

        $template = NotificationTemplate::create([
            'name' => 'Original Template',
            'type' => 'email',
            'event' => 'test_event',
            'subject' => 'Original Subject',
            'content' => 'Original Content',
            'created_by' => $this->admin->id,
        ]);

        $updateData = [
            'name' => 'Updated Template',
            'subject' => 'Updated Subject',
            'content' => 'Updated Content',
        ];

        $response = $this->putJson("/api/v1/admin/notifications/templates/{$template->id}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('notification_templates', [
            'id' => $template->id,
            'name' => 'Updated Template',
            'subject' => 'Updated Subject',
            'content' => 'Updated Content',
        ]);
    }

    public function test_can_get_notification_preferences()
    {
        Sanctum::actingAs($this->admin);

        // Create a test preference
        NotificationPreference::create([
            'user_type' => 'admin',
            'user_id' => $this->admin->id,
            'notification_type' => 'test_notification',
            'email_enabled' => true,
            'in_app_enabled' => false,
        ]);

        $response = $this->getJson('/api/v1/admin/notifications/preferences');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'message'
                ]);
    }

    public function test_can_update_notification_preferences()
    {
        Sanctum::actingAs($this->admin);

        $data = [
            'preferences' => [
                'test_notification' => [
                    'email_enabled' => false,
                    'in_app_enabled' => true,
                    'sms_enabled' => false,
                ],
                'another_notification' => [
                    'email_enabled' => true,
                    'in_app_enabled' => true,
                    'sms_enabled' => false,
                ],
            ],
        ];

        $response = $this->putJson('/api/v1/admin/notifications/preferences', $data);

        $response->assertStatus(200);

        // Verify preferences were created/updated
        $this->assertDatabaseHas('notification_preferences', [
            'user_type' => 'admin',
            'user_id' => $this->admin->id,
            'notification_type' => 'test_notification',
            'email_enabled' => false,
            'in_app_enabled' => true,
        ]);
    }

    public function test_notification_service_can_send_notification()
    {
        // Create a template
        NotificationTemplate::create([
            'name' => 'Test Service Template',
            'type' => 'in_app',
            'event' => 'test_service_event',
            'subject' => 'Hello {{name}}',
            'content' => 'Welcome {{name}} to {{institute}}',
            'variables' => ['name', 'institute'],
        ]);

        $variables = [
            'name' => $this->student->name,
            'institute' => $this->institute->name,
        ];

        $result = $this->notificationService->sendNotification(
            'student',
            $this->student->id,
            'test_service_event',
            $variables,
            ['in_app']
        );

        $this->assertTrue($result['in_app']);

        // Verify notification was logged
        $this->assertDatabaseHas('notification_logs', [
            'recipient_type' => 'student',
            'recipient_id' => $this->student->id,
            'event' => 'test_service_event',
            'status' => 'sent',
        ]);
    }

    public function test_notification_validation()
    {
        Sanctum::actingAs($this->admin);

        // Test invalid recipient type
        $response = $this->postJson('/api/v1/admin/notifications/send', [
            'recipient_type' => 'invalid',
            'recipient_ids' => [1],
            'subject' => 'Test',
            'content' => 'Test',
            'channels' => ['in_app'],
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['recipient_type']);

        // Test missing required fields
        $response = $this->postJson('/api/v1/admin/notifications/send', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['recipient_type', 'recipient_ids', 'subject', 'content', 'channels']);
    }
}

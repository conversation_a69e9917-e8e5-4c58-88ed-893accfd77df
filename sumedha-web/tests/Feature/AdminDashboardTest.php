<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Institute;
use App\Models\Student;
use App\Models\Course;
use App\Models\AdminRole;
use App\Models\Document;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $institute;
    protected $superAdmin;
    protected $admin;
    protected $counselor;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        $permissions = [
            'view_analytics', 'manage_users', 'manage_roles', 'manage_students',
            'manage_courses', 'manage_documents', 'manage_fees'
        ];
        
        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles
        $superAdminRole = Role::create(['name' => 'super_admin']);
        $adminRole = Role::create(['name' => 'admin']);
        $counselorRole = Role::create(['name' => 'counselor']);
        
        $superAdminRole->givePermissionTo(Permission::all());
        $adminRole->givePermissionTo(['view_analytics', 'manage_users', 'manage_students', 'manage_courses']);
        $counselorRole->givePermissionTo(['view_analytics', 'manage_students']);

        // Create institute
        $this->institute = Institute::factory()->create();

        // Create admin roles
        AdminRole::createDefaults();

        // Create users
        $this->superAdmin = User::factory()->create([
            'role' => 'super_admin',
            'user_type' => 'super_admin',
            'institute_id' => null,
        ]);
        $this->superAdmin->assignRole('super_admin');

        $this->admin = User::factory()->create([
            'role' => 'admin',
            'user_type' => 'admin',
            'institute_id' => $this->institute->id,
            'admin_role_id' => AdminRole::getBySlug('admin')->id,
        ]);
        $this->admin->assignRole('admin');

        $this->counselor = User::factory()->create([
            'role' => 'staff',
            'user_type' => 'counselor',
            'institute_id' => $this->institute->id,
            'admin_role_id' => AdminRole::getBySlug('counselor')->id,
        ]);
        $this->counselor->assignRole('counselor');
    }

    /** @test */
    public function super_admin_can_access_dashboard()
    {
        Sanctum::actingAs($this->superAdmin);

        $response = $this->getJson('/api/v1/admin/dashboard');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'metrics',
                        'recent_activities',
                        'chart_data',
                        'user_permissions'
                    ]
                ]);
    }

    /** @test */
    public function admin_can_access_dashboard_for_their_institute()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'metrics',
                        'recent_activities',
                        'chart_data',
                        'user_permissions'
                    ]
                ]);
    }

    /** @test */
    public function unauthorized_user_cannot_access_dashboard()
    {
        $unauthorizedUser = User::factory()->create([
            'role' => 'staff',
            'user_type' => 'teacher',
            'institute_id' => $this->institute->id,
        ]);

        Sanctum::actingAs($unauthorizedUser);

        $response = $this->getJson('/api/v1/admin/dashboard');

        $response->assertStatus(403);
    }

    /** @test */
    public function dashboard_returns_correct_metrics()
    {
        // Create test data
        Student::factory()->count(5)->create(['institute_id' => $this->institute->id]);
        Course::factory()->count(3)->create(['status' => 'active']);

        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard');

        $response->assertStatus(200);
        
        $metrics = $response->json('data.metrics');
        $this->assertArrayHasKey('total_students', $metrics);
        $this->assertArrayHasKey('active_courses', $metrics);
        $this->assertArrayHasKey('pending_documents', $metrics);
        $this->assertArrayHasKey('total_revenue', $metrics);
    }

    /** @test */
    public function admin_can_view_analytics()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard/analytics?period=30');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'overview',
                        'trends',
                        'performance'
                    ]
                ]);
    }

    /** @test */
    public function admin_can_view_users_list()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard/users');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'data',
                        'current_page',
                        'total'
                    ]
                ]);
    }

    /** @test */
    public function admin_can_create_new_user()
    {
        Sanctum::actingAs($this->admin);

        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '+977-9876543210',
            'user_type' => 'counselor',
            'institute_id' => $this->institute->id,
        ];

        $response = $this->postJson('/api/v1/admin/dashboard/users', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data',
                    'temp_password'
                ]);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'user_type' => 'counselor',
            'institute_id' => $this->institute->id,
        ]);
    }

    /** @test */
    public function admin_can_update_user()
    {
        $user = User::factory()->create([
            'institute_id' => $this->institute->id,
            'user_type' => 'teacher',
        ]);

        Sanctum::actingAs($this->admin);

        $updateData = [
            'name' => 'Updated Name',
            'user_type' => 'counselor',
        ];

        $response = $this->putJson("/api/v1/admin/dashboard/users/{$user->id}", $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User updated successfully'
                ]);

        $this->assertDatabaseHas('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'user_type' => 'counselor',
        ]);
    }

    /** @test */
    public function admin_can_delete_user()
    {
        $user = User::factory()->create([
            'institute_id' => $this->institute->id,
        ]);

        Sanctum::actingAs($this->admin);

        $response = $this->deleteJson("/api/v1/admin/dashboard/users/{$user->id}");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'User deleted successfully'
                ]);

        $this->assertDatabaseMissing('users', [
            'id' => $user->id,
        ]);
    }

    /** @test */
    public function admin_cannot_delete_themselves()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->deleteJson("/api/v1/admin/dashboard/users/{$this->admin->id}");

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cannot delete your own account'
                ]);
    }

    /** @test */
    public function admin_can_view_roles()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard/roles');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'slug',
                            'description',
                            'permissions',
                            'priority'
                        ]
                    ]
                ]);
    }

    /** @test */
    public function admin_can_create_role()
    {
        Sanctum::actingAs($this->admin);

        $roleData = [
            'name' => 'Test Role',
            'description' => 'Test role description',
            'permissions' => ['manage_students', 'view_analytics'],
            'priority' => 50,
        ];

        $response = $this->postJson('/api/v1/admin/dashboard/roles', $roleData);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Role created successfully'
                ]);

        $this->assertDatabaseHas('admin_roles', [
            'name' => 'Test Role',
            'description' => 'Test role description',
            'priority' => 50,
        ]);
    }

    /** @test */
    public function admin_can_view_permissions()
    {
        Sanctum::actingAs($this->admin);

        $response = $this->getJson('/api/v1/admin/dashboard/permissions');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data'
                ]);
    }

    /** @test */
    public function counselor_has_limited_access()
    {
        Sanctum::actingAs($this->counselor);

        // Can access dashboard
        $response = $this->getJson('/api/v1/admin/dashboard');
        $response->assertStatus(200);

        // Cannot manage users
        $response = $this->getJson('/api/v1/admin/dashboard/users');
        $response->assertStatus(403);

        // Cannot manage roles
        $response = $this->getJson('/api/v1/admin/dashboard/roles');
        $response->assertStatus(403);
    }
}

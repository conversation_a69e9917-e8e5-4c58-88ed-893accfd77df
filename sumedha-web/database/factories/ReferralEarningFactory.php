<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Institute;
use App\Models\Student;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReferralEarning>
 */
class ReferralEarningFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $baseAmount = $this->faker->randomFloat(2, 100, 5000);
        $commissionRate = $this->faker->randomFloat(2, 5, 15);
        $commissionAmount = round(($baseAmount * $commissionRate) / 100, 2);

        return [
            'institute_id' => Institute::factory(),
            'student_id' => Student::factory(),
            'commission_amount' => $commissionAmount,
            'commission_rate' => $commissionRate,
            'base_amount' => $baseAmount,
            'status' => $this->faker->randomElement(['pending', 'approved', 'paid', 'rejected']),
            'payout_method' => $this->faker->optional(0.3)->randomElement(['esewa', 'khalti', 'bank_transfer']),
            'payout_details' => $this->faker->optional(0.3)->phoneNumber(),
            'earned_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'paid_at' => $this->faker->optional(0.2)->dateTimeBetween('-3 months', 'now'),
            'processed_by' => $this->faker->optional(0.3)->randomElement([User::factory(), null]),
            'notes' => $this->faker->optional(0.4)->sentence(),
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'paid_at' => null,
                'payout_method' => null,
                'payout_details' => null,
            ];
        });
    }

    public function approved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'approved',
                'paid_at' => null,
            ];
        });
    }

    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'paid',
                'paid_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
                'payout_method' => $this->faker->randomElement(['esewa', 'khalti', 'bank_transfer']),
                'payout_details' => $this->faker->phoneNumber(),
                'processed_by' => User::factory(),
            ];
        });
    }
}

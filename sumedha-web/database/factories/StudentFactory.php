<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use App\Models\Institute;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'institute_id' => Institute::factory(),
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'date_of_birth' => $this->faker->date('Y-m-d', '-18 years'),
            'gender' => $this->faker->randomElement(['male', 'female', 'other']),
            'nationality' => $this->faker->country(),
            'passport_number' => $this->faker->optional()->regexify('[A-Z]{2}[0-9]{7}'),
            'password' => Hash::make('password'),
            'status' => $this->faker->randomElement(['active', 'inactive', 'pending']),
            'referral_source' => $this->faker->randomElement(['link', 'qr_code', 'direct']),
            'registered_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
            ];
        });
    }

    public function withPassport()
    {
        return $this->state(function (array $attributes) {
            return [
                'passport_number' => $this->faker->regexify('[A-Z]{2}[0-9]{7}'),
            ];
        });
    }
}

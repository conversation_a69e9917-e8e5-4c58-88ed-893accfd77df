<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Institute;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReferralCode>
 */
class ReferralCodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'institute_id' => Institute::factory(),
            'code' => strtoupper($this->faker->unique()->lexify('????????')),
            'type' => $this->faker->randomElement(['general', 'campaign', 'special', 'limited']),
            'status' => $this->faker->randomElement(['active', 'inactive']),
            'usage_limit' => $this->faker->optional(0.7)->numberBetween(10, 1000),
            'usage_count' => 0,
            'expires_at' => $this->faker->optional(0.3)->dateTimeBetween('now', '+1 year'),
            'description' => $this->faker->optional()->sentence(),
            'commission_rate' => $this->faker->optional(0.5)->randomFloat(2, 5, 20),
            'created_by' => User::factory(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
                'expires_at' => $this->faker->dateTimeBetween('+1 day', '+1 year'),
            ];
        });
    }

    public function expired()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'expired',
                'expires_at' => $this->faker->dateTimeBetween('-1 year', '-1 day'),
            ];
        });
    }

    public function unlimited()
    {
        return $this->state(function (array $attributes) {
            return [
                'usage_limit' => null,
                'expires_at' => null,
            ];
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reward_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->integer('points'); // Can be negative for deductions
            $table->enum('type', ['referral', 'course_completion', 'milestone', 'bonus', 'deduction', 'redemption'])->default('bonus');
            $table->text('description')->nullable();
            $table->string('reference_type')->nullable(); // Polymorphic relation
            $table->unsignedBigInteger('reference_id')->nullable(); // Polymorphic relation
            $table->timestamp('expires_at')->nullable();
            $table->enum('status', ['active', 'expired', 'cancelled'])->default('active');
            $table->timestamps();

            $table->index(['institute_id', 'student_id']);
            $table->index(['student_id', 'status']);
            $table->index(['type']);
            $table->index(['expires_at']);
            $table->index(['reference_type', 'reference_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reward_points');
    }
};

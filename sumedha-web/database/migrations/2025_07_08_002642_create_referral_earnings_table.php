<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_earnings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->decimal('commission_amount', 10, 2);
            $table->decimal('commission_rate', 5, 2); // Store rate at time of earning
            $table->decimal('base_amount', 10, 2); // Amount commission was calculated from
            $table->enum('status', ['pending', 'approved', 'paid', 'rejected'])->default('pending');
            $table->enum('payout_method', ['esewa', 'khalti', 'bank_transfer'])->nullable();
            $table->string('payout_details')->nullable(); // Account details
            $table->timestamp('earned_at')->useCurrent();
            $table->timestamp('paid_at')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['institute_id', 'status']);
            $table->index(['student_id']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_earnings');
    }
};

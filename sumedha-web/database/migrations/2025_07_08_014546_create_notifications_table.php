<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // notification type class
            $table->morphs('notifiable'); // polymorphic relation (user_id, user_type)
            $table->text('data'); // JSON data
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // Custom notifications table for our specific needs
        Schema::create('custom_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('recipient_type', ['student', 'admin', 'all_students', 'all_admins']);
            $table->foreignId('recipient_id')->nullable(); // specific user ID if not broadcast
            $table->enum('notification_type', [
                'new_student_referral',
                'document_upload',
                'document_verification_request',
                'document_submission_to_college',
                'new_chat_message',
                'fee_payment_reminder',
                'application_progress_update',
                'new_post',
                'class_reminder'
            ]);
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Additional data
            $table->enum('delivery_method', ['email', 'in_app', 'both'])->default('both');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('email_sent')->default(false);
            $table->timestamp('email_sent_at')->nullable();
            $table->timestamps();

            $table->index(['recipient_type', 'recipient_id']);
            $table->index(['notification_type']);
            $table->index(['is_read']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_notifications');
        Schema::dropIfExists('notifications');
    }
};

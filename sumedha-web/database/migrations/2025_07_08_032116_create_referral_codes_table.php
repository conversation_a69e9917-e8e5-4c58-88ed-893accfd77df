<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_codes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->constrained()->onDelete('cascade');
            $table->string('code', 20)->unique();
            $table->enum('type', ['general', 'campaign', 'special', 'limited'])->default('general');
            $table->enum('status', ['active', 'inactive', 'expired', 'exhausted'])->default('active');
            $table->integer('usage_limit')->nullable(); // null = unlimited
            $table->integer('usage_count')->default(0);
            $table->timestamp('expires_at')->nullable();
            $table->text('description')->nullable();
            $table->decimal('commission_rate', 5, 2)->nullable(); // Override institute rate if set
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();

            $table->index(['institute_id', 'status']);
            $table->index(['code']);
            $table->index(['status']);
            $table->index(['expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_codes');
    }
};

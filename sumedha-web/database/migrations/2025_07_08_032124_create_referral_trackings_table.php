<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referral_trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->constrained()->onDelete('cascade');
            $table->foreignId('referral_code_id')->constrained()->onDelete('cascade');
            $table->foreignId('student_id')->nullable()->constrained()->onDelete('cascade');
            $table->enum('referral_source', ['link', 'qr_code', 'direct', 'social', 'email', 'other'])->default('link');
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->enum('conversion_status', ['pending', 'converted', 'failed'])->default('pending');
            $table->timestamp('converted_at')->nullable();
            $table->json('metadata')->nullable(); // Additional tracking data
            $table->timestamps();

            $table->index(['institute_id', 'conversion_status']);
            $table->index(['referral_code_id']);
            $table->index(['student_id']);
            $table->index(['conversion_status']);
            $table->index(['referral_source']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referral_trackings');
    }
};

<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Institute;
use App\Models\User;
use App\Models\Course;
use App\Models\DocumentType;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create document types
        $documentTypes = [
            [
                'name' => 'Passport',
                'description' => 'Valid passport document',
                'is_required' => true,
                'auto_accept' => true,
                'allowed_formats' => ['pdf', 'jpg', 'jpeg', 'png'],
                'max_file_size_mb' => 5,
                'sort_order' => 1,
            ],
            [
                'name' => 'Academic Certificates',
                'description' => 'Educational certificates and transcripts',
                'is_required' => true,
                'auto_accept' => true,
                'allowed_formats' => ['pdf', 'jpg', 'jpeg', 'png'],
                'max_file_size_mb' => 10,
                'sort_order' => 2,
            ],
            [
                'name' => 'National ID',
                'description' => 'National identity card or citizenship certificate',
                'is_required' => true,
                'auto_accept' => true,
                'allowed_formats' => ['pdf', 'jpg', 'jpeg', 'png'],
                'max_file_size_mb' => 5,
                'sort_order' => 3,
            ],
            [
                'name' => 'Financial Proof',
                'description' => 'Bank statements or financial documents',
                'is_required' => true,
                'auto_accept' => false,
                'allowed_formats' => ['pdf', 'jpg', 'jpeg', 'png'],
                'max_file_size_mb' => 10,
                'sort_order' => 4,
            ],
            [
                'name' => 'Personal Photo',
                'description' => 'Recent passport-size photograph',
                'is_required' => true,
                'auto_accept' => false,
                'allowed_formats' => ['jpg', 'jpeg', 'png'],
                'max_file_size_mb' => 2,
                'sort_order' => 5,
            ],
        ];

        foreach ($documentTypes as $docType) {
            DocumentType::firstOrCreate(
                ['name' => $docType['name']],
                $docType
            );
        }

        // Create sample courses
        $courses = [
            [
                'name' => 'Japanese N5 Level',
                'description' => 'Beginner level Japanese language course',
                'fee' => 15000.00,
                'duration_months' => 6,
                'level' => 'N5',
                'status' => 'active',
            ],
            [
                'name' => 'Japanese N4 Level',
                'description' => 'Elementary level Japanese language course',
                'fee' => 18000.00,
                'duration_months' => 8,
                'level' => 'N4',
                'status' => 'active',
            ],
            [
                'name' => 'Japanese N3 Level',
                'description' => 'Intermediate level Japanese language course',
                'fee' => 22000.00,
                'duration_months' => 10,
                'level' => 'N3',
                'status' => 'active',
            ],
        ];

        foreach ($courses as $course) {
            Course::firstOrCreate(
                ['name' => $course['name']],
                $course
            );
        }

        // Create sample institute
        $institute = Institute::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sumedha Japanese Language Institute',
                'address' => 'Kathmandu, Nepal',
                'phone' => '+977-1-4567890',
                'email' => '<EMAIL>',
                'status' => 'active',
                'commission_rate' => 10.00,
                'description' => 'Leading Japanese language institute in Nepal',
            ]
        );

        // Create super admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'user_type' => 'super_admin',
                'status' => 'active',
            ]
        );
        $superAdmin->assignRole('super_admin');

        // Create institute admin
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'institute_id' => $institute->id,
                'name' => 'Institute Administrator',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+977-9876543210',
                'user_type' => 'admin',
                'status' => 'active',
            ]
        );
        $admin->assignRole('admin');

        // Create sample counselor
        $counselor = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'institute_id' => $institute->id,
                'name' => 'Student Counselor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+977-9876543211',
                'user_type' => 'counselor',
                'status' => 'active',
            ]
        );
        $counselor->assignRole('counselor');
    }
}

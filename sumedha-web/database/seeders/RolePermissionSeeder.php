<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Institute management
            'manage_institute',
            'view_institute',
            'edit_institute',

            // Student management
            'manage_students',
            'view_students',
            'create_students',
            'edit_students',
            'delete_students',

            // Course management
            'manage_courses',
            'view_courses',
            'create_courses',
            'edit_courses',
            'delete_courses',

            // Document management
            'manage_documents',
            'view_documents',
            'verify_documents',
            'download_documents',

            // Chat management
            'manage_chat',
            'view_chat',
            'send_messages',

            // Fee management
            'manage_fees',
            'view_fees',
            'update_fees',

            // Progress management
            'manage_progress',
            'view_progress',
            'update_progress',

            // Staff management
            'manage_staff',
            'invite_staff',
            'remove_staff',

            // Reports and analytics
            'view_reports',
            'export_data',

            // Posts and announcements
            'manage_posts',
            'create_posts',
            'edit_posts',
            'delete_posts',

            // Online classes
            'manage_classes',
            'schedule_classes',
            'conduct_classes',

            // Notifications
            'manage_notifications',
            'send_notifications',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::firstOrCreate(['name' => 'super_admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Main Admin - has most permissions for their institute
        $admin = Role::firstOrCreate(['name' => 'admin']);
        $admin->givePermissionTo([
            'manage_institute', 'view_institute', 'edit_institute',
            'manage_students', 'view_students', 'create_students', 'edit_students',
            'manage_courses', 'view_courses', 'create_courses', 'edit_courses',
            'manage_documents', 'view_documents', 'verify_documents', 'download_documents',
            'manage_chat', 'view_chat', 'send_messages',
            'manage_fees', 'view_fees', 'update_fees',
            'manage_progress', 'view_progress', 'update_progress',
            'manage_staff', 'invite_staff', 'remove_staff',
            'view_reports', 'export_data',
            'manage_posts', 'create_posts', 'edit_posts', 'delete_posts',
            'manage_classes', 'schedule_classes', 'conduct_classes',
            'manage_notifications', 'send_notifications',
        ]);

        // Counselor - focused on student management and communication
        $counselor = Role::firstOrCreate(['name' => 'counselor']);
        $counselor->givePermissionTo([
            'view_institute',
            'manage_students', 'view_students', 'create_students', 'edit_students',
            'view_courses',
            'manage_documents', 'view_documents', 'verify_documents',
            'manage_chat', 'view_chat', 'send_messages',
            'view_progress', 'update_progress',
            'view_reports',
            'create_posts', 'edit_posts',
            'send_notifications',
        ]);

        // Accountant - focused on financial management
        $accountant = Role::firstOrCreate(['name' => 'accountant']);
        $accountant->givePermissionTo([
            'view_institute',
            'view_students',
            'view_courses',
            'manage_fees', 'view_fees', 'update_fees',
            'view_progress',
            'view_reports', 'export_data',
            'view_chat', 'send_messages',
        ]);

        // Teacher - focused on classes and student interaction
        $teacher = Role::firstOrCreate(['name' => 'teacher']);
        $teacher->givePermissionTo([
            'view_institute',
            'view_students',
            'view_courses',
            'view_documents',
            'manage_chat', 'view_chat', 'send_messages',
            'view_progress',
            'manage_classes', 'schedule_classes', 'conduct_classes',
            'create_posts',
        ]);
    }
}

<?php

namespace App\Services;

use App\Models\NotificationTemplate;
use App\Models\NotificationPreference;
use App\Models\NotificationLog;
use App\Models\User;
use App\Models\Student;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send notification to user
     */
    public function sendNotification($recipientType, $recipientId, $event, $variables = [], $channels = ['email', 'in_app'])
    {
        $recipient = $this->getRecipient($recipientType, $recipientId);
        
        if (!$recipient) {
            Log::error("Recipient not found: {$recipientType}:{$recipientId}");
            return false;
        }

        $results = [];
        
        foreach ($channels as $channel) {
            // Check if user wants to receive this type of notification
            if (!NotificationPreference::shouldReceive($recipientType, $recipientId, $event, $channel)) {
                continue;
            }

            $template = NotificationTemplate::getTemplate($event, $channel);
            
            if (!$template) {
                Log::warning("No template found for event: {$event}, channel: {$channel}");
                continue;
            }

            $subject = $template->renderSubject($variables);
            $content = $template->renderContent($variables);

            // Create notification log
            $log = NotificationLog::createLog(
                $recipientType,
                $recipientId,
                $channel,
                $event,
                $subject,
                $content,
                $template->id
            );

            // Send notification based on channel
            $result = $this->sendByChannel($channel, $recipient, $subject, $content, $log);
            $results[$channel] = $result;
        }

        return $results;
    }

    /**
     * Send notification by specific channel
     */
    protected function sendByChannel($channel, $recipient, $subject, $content, $log)
    {
        try {
            switch ($channel) {
                case 'email':
                    return $this->sendEmail($recipient, $subject, $content, $log);
                case 'in_app':
                    return $this->sendInApp($recipient, $subject, $content, $log);
                case 'sms':
                    return $this->sendSms($recipient, $content, $log);
                default:
                    throw new \Exception("Unsupported channel: {$channel}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to send notification via {$channel}: " . $e->getMessage());
            $log->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Send email notification
     */
    protected function sendEmail($recipient, $subject, $content, $log)
    {
        try {
            Mail::send([], [], function ($message) use ($recipient, $subject, $content) {
                $message->to($recipient->email, $recipient->name)
                       ->subject($subject)
                       ->html($content);
            });

            $log->markAsSent();
            return true;
        } catch (\Exception $e) {
            $log->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Send in-app notification
     */
    protected function sendInApp($recipient, $subject, $content, $log)
    {
        // For in-app notifications, we just mark as sent since they're stored in the database
        $log->markAsSent();
        return true;
    }

    /**
     * Send SMS notification
     */
    protected function sendSms($recipient, $content, $log)
    {
        // SMS implementation would go here
        // For now, just mark as sent
        $log->markAsSent();
        return true;
    }

    /**
     * Get recipient model
     */
    protected function getRecipient($recipientType, $recipientId)
    {
        if ($recipientType === 'admin') {
            return User::find($recipientId);
        } else {
            return Student::find($recipientId);
        }
    }

    /**
     * Send bulk notifications
     */
    public function sendBulkNotification($recipientType, $recipientIds, $event, $variables = [], $channels = ['email', 'in_app'])
    {
        $results = [];
        
        foreach ($recipientIds as $recipientId) {
            $results[$recipientId] = $this->sendNotification($recipientType, $recipientId, $event, $variables, $channels);
        }
        
        return $results;
    }

    /**
     * Send notification to all users of a type
     */
    public function sendToAllUsers($recipientType, $event, $variables = [], $channels = ['email', 'in_app'], $instituteId = null)
    {
        if ($recipientType === 'admin') {
            $query = User::query();
            if ($instituteId) {
                $query->where('institute_id', $instituteId);
            }
        } else {
            $query = Student::query();
            if ($instituteId) {
                $query->where('institute_id', $instituteId);
            }
        }

        $recipients = $query->pluck('id')->toArray();
        
        return $this->sendBulkNotification($recipientType, $recipients, $event, $variables, $channels);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $recipientType, $recipientId)
    {
        $notification = NotificationLog::forRecipient($recipientType, $recipientId)
                                      ->where('id', $notificationId)
                                      ->first();
        
        if ($notification) {
            $notification->markAsRead();
            return true;
        }
        
        return false;
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead($recipientType, $recipientId)
    {
        return NotificationLog::forRecipient($recipientType, $recipientId)
                             ->byType('in_app')
                             ->unread()
                             ->update(['read_at' => now()]);
    }

    /**
     * Get notifications for user
     */
    public function getNotificationsForUser($recipientType, $recipientId, $limit = 20, $offset = 0)
    {
        return NotificationLog::forRecipient($recipientType, $recipientId)
                             ->byType('in_app')
                             ->orderBy('created_at', 'desc')
                             ->offset($offset)
                             ->limit($limit)
                             ->get();
    }

    /**
     * Get unread count for user
     */
    public function getUnreadCount($recipientType, $recipientId)
    {
        return NotificationLog::getUnreadCount($recipientType, $recipientId);
    }

    /**
     * Retry failed notifications
     */
    public function retryFailedNotifications()
    {
        $failedNotifications = NotificationLog::failed()
                                            ->where('next_retry_at', '<=', now())
                                            ->where('retry_count', '<', 3)
                                            ->get();

        foreach ($failedNotifications as $notification) {
            $recipient = $this->getRecipient($notification->recipient_type, $notification->recipient_id);
            
            if ($recipient) {
                $this->sendByChannel(
                    $notification->type,
                    $recipient,
                    $notification->subject,
                    $notification->content,
                    $notification
                );
            }
        }

        return $failedNotifications->count();
    }

    /**
     * Send welcome notification to new student
     */
    public function sendWelcomeNotification($studentId, $instituteId)
    {
        $student = Student::find($studentId);
        $institute = $student->institute;

        $variables = [
            'student_name' => $student->name,
            'institute_name' => $institute->name,
            'login_url' => config('app.url') . '/student/login',
        ];

        return $this->sendNotification('student', $studentId, 'student_registered', $variables);
    }

    /**
     * Send document upload notification
     */
    public function sendDocumentUploadNotification($studentId, $documentType)
    {
        $student = Student::find($studentId);
        
        $variables = [
            'student_name' => $student->name,
            'document_type' => $documentType,
            'document_url' => config('app.url') . '/admin/students/' . $studentId . '/documents',
        ];

        // Send to all admins of the institute
        return $this->sendToAllUsers('admin', 'document_uploaded', $variables, ['email', 'in_app'], $student->institute_id);
    }

    /**
     * Send progress update notification
     */
    public function sendProgressUpdateNotification($studentId, $oldStage, $newStage, $updatedBy)
    {
        $student = Student::find($studentId);
        $updater = User::find($updatedBy);
        
        $variables = [
            'student_name' => $student->name,
            'old_stage' => $oldStage,
            'new_stage' => $newStage,
            'updated_by' => $updater->name,
        ];

        return $this->sendNotification('student', $studentId, 'progress_updated', $variables);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RewardPoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'institute_id',
        'student_id',
        'points',
        'type',
        'description',
        'reference_type',
        'reference_id',
        'expires_at',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'points' => 'integer',
            'expires_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function reference()
    {
        return $this->morphTo();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    public function scopeByInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeByStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeEarned($query)
    {
        return $query->where('points', '>', 0);
    }

    public function scopeSpent($query)
    {
        return $query->where('points', '<', 0);
    }

    /**
     * Helper methods
     */
    public function isExpired()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isActive()
    {
        return $this->status === 'active' && !$this->isExpired();
    }

    /**
     * Award points to a student
     */
    public static function awardPoints($instituteId, $studentId, $points, $type, $description = null, $reference = null, $expiresAt = null)
    {
        $data = [
            'institute_id' => $instituteId,
            'student_id' => $studentId,
            'points' => $points,
            'type' => $type,
            'description' => $description,
            'expires_at' => $expiresAt,
            'status' => 'active',
        ];

        if ($reference) {
            $data['reference_type'] = get_class($reference);
            $data['reference_id'] = $reference->id;
        }

        return self::create($data);
    }

    /**
     * Deduct points from a student
     */
    public static function deductPoints($instituteId, $studentId, $points, $type, $description = null, $reference = null)
    {
        return self::awardPoints($instituteId, $studentId, -$points, $type, $description, $reference);
    }

    /**
     * Get total active points for a student
     */
    public static function getTotalPoints($studentId, $instituteId = null)
    {
        $query = self::where('student_id', $studentId)->active();

        if ($instituteId) {
            $query->byInstitute($instituteId);
        }

        return $query->sum('points');
    }

    /**
     * Get points breakdown for a student
     */
    public static function getPointsBreakdown($studentId, $instituteId = null)
    {
        $query = self::where('student_id', $studentId);

        if ($instituteId) {
            $query->byInstitute($instituteId);
        }

        $earned = $query->clone()->earned()->active()->sum('points');
        $spent = abs($query->clone()->spent()->sum('points'));
        $expired = $query->clone()->expired()->earned()->sum('points');
        $total = $query->clone()->active()->sum('points');

        return [
            'total_earned' => $earned,
            'total_spent' => $spent,
            'total_expired' => $expired,
            'current_balance' => $total,
        ];
    }

    /**
     * Award referral points
     */
    public static function awardReferralPoints($instituteId, $studentId, $referredStudentId, $points = 100)
    {
        $referredStudent = Student::find($referredStudentId);
        
        return self::awardPoints(
            $instituteId,
            $studentId,
            $points,
            'referral',
            "Referral bonus for referring {$referredStudent->name}",
            $referredStudent
        );
    }

    /**
     * Award course completion points
     */
    public static function awardCourseCompletionPoints($instituteId, $studentId, $courseId, $points = 50)
    {
        $course = Course::find($courseId);
        
        return self::awardPoints(
            $instituteId,
            $studentId,
            $points,
            'course_completion',
            "Course completion bonus for {$course->name}",
            $course
        );
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class AdminRole extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'permissions',
        'is_active',
        'priority',
    ];

    protected function casts(): array
    {
        return [
            'permissions' => 'array',
            'is_active' => 'boolean',
            'priority' => 'integer',
        ];
    }

    /**
     * Boot method to generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($role) {
            if (empty($role->slug)) {
                $role->slug = Str::slug($role->name);
            }
        });
    }

    /**
     * Relationships
     */
    public function users()
    {
        return $this->hasMany(User::class, 'admin_role_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Helper methods
     */
    public function hasPermission($permission)
    {
        return in_array($permission, $this->permissions ?? []);
    }

    public function addPermission($permission)
    {
        $permissions = $this->permissions ?? [];
        if (!in_array($permission, $permissions)) {
            $permissions[] = $permission;
            $this->update(['permissions' => $permissions]);
        }
    }

    public function removePermission($permission)
    {
        $permissions = $this->permissions ?? [];
        $permissions = array_filter($permissions, function($p) use ($permission) {
            return $p !== $permission;
        });
        $this->update(['permissions' => array_values($permissions)]);
    }

    public function syncPermissions($permissions)
    {
        $this->update(['permissions' => $permissions]);
    }

    /**
     * Create default admin roles
     */
    public static function createDefaults()
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Full access to all features and settings',
                'permissions' => [
                    'manage_users', 'manage_roles', 'manage_students', 'manage_courses',
                    'manage_documents', 'manage_fees', 'manage_referrals', 'manage_notifications',
                    'view_analytics', 'manage_settings', 'export_data'
                ],
                'priority' => 100,
            ],
            [
                'name' => 'Admin',
                'slug' => 'admin',
                'description' => 'Standard admin access with most features',
                'permissions' => [
                    'manage_students', 'manage_courses', 'manage_documents', 'manage_fees',
                    'view_analytics', 'manage_notifications'
                ],
                'priority' => 80,
            ],
            [
                'name' => 'Counselor',
                'slug' => 'counselor',
                'description' => 'Student counseling and document management',
                'permissions' => [
                    'manage_students', 'manage_documents', 'view_analytics'
                ],
                'priority' => 60,
            ],
            [
                'name' => 'Accountant',
                'slug' => 'accountant',
                'description' => 'Financial management and fee tracking',
                'permissions' => [
                    'manage_fees', 'view_analytics', 'export_data'
                ],
                'priority' => 40,
            ],
        ];

        foreach ($roles as $role) {
            self::firstOrCreate(['slug' => $role['slug']], $role);
        }
    }

    /**
     * Get role by slug
     */
    public static function getBySlug($slug)
    {
        return self::where('slug', $slug)->first();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'fee',
        'duration_months',
        'level',
        'status',
        'curriculum',
    ];

    protected function casts(): array
    {
        return [
            'fee' => 'decimal:2',
            'curriculum' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_courses')
                    ->withPivot([
                        'total_course_fee',
                        'documentation_fee',
                        'amount_received',
                        'remaining_fee',
                        'payment_status',
                        'payment_notes',
                        'enrolled_at',
                        'completed_at',
                        'status'
                    ])
                    ->withTimestamps();
    }

    public function onlineClasses()
    {
        return $this->hasMany(OnlineClass::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByLevel($query, $level)
    {
        return $query->where('level', $level);
    }

    /**
     * Helper methods
     */
    public function getEnrolledStudentsCountAttribute()
    {
        return $this->students()->count();
    }

    public function getFormattedFeeAttribute()
    {
        return number_format($this->fee, 2);
    }

    public function getDurationTextAttribute()
    {
        return $this->duration_months . ' month' . ($this->duration_months > 1 ? 's' : '');
    }
}

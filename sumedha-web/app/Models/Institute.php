<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Institute extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'referral_link',
        'qr_code_path',
        'status',
        'commission_rate',
        'description',
    ];

    protected function casts(): array
    {
        return [
            'commission_rate' => 'decimal:2',
        ];
    }

    /**
     * Boot method to generate referral link
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($institute) {
            if (empty($institute->referral_link)) {
                $institute->referral_link = Str::random(32);
            }
        });
    }

    /**
     * Relationships
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function students()
    {
        return $this->hasMany(Student::class);
    }

    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    public function onlineClasses()
    {
        return $this->hasMany(OnlineClass::class);
    }

    public function referralEarnings()
    {
        return $this->hasMany(ReferralEarning::class);
    }

    public function referralCodes()
    {
        return $this->hasMany(ReferralCode::class);
    }

    public function referralTrackings()
    {
        return $this->hasMany(ReferralTracking::class);
    }

    public function rewardPoints()
    {
        return $this->hasMany(RewardPoint::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Helper methods
     */
    public function getTotalStudentsAttribute()
    {
        return $this->students()->count();
    }

    public function getSubscribedStudentsAttribute()
    {
        return $this->students()->whereHas('courses')->count();
    }

    public function getTotalEarningsAttribute()
    {
        return $this->referralEarnings()->where('status', 'approved')->sum('commission_amount');
    }

    public function getWithdrawableBalanceAttribute()
    {
        return $this->referralEarnings()->where('status', 'pending')->sum('commission_amount');
    }

    public function generateQrCode()
    {
        // QR code generation logic will be implemented later
        $qrCodePath = 'qr_codes/' . $this->id . '_' . time() . '.png';
        $this->update(['qr_code_path' => $qrCodePath]);
        return $qrCodePath;
    }
}

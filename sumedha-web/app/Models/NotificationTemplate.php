<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'event',
        'subject',
        'content',
        'variables',
        'is_active',
        'language',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'variables' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function notificationLogs()
    {
        return $this->hasMany(NotificationLog::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByEvent($query, $event)
    {
        return $query->where('event', $event);
    }

    public function scopeByLanguage($query, $language)
    {
        return $query->where('language', $language);
    }

    /**
     * Helper methods
     */
    public function renderContent($variables = [])
    {
        $content = $this->content;

        foreach ($variables as $key => $value) {
            $content = str_replace('{{' . $key . '}}', $value, $content);
        }

        return $content;
    }

    public function renderSubject($variables = [])
    {
        $subject = $this->subject;

        foreach ($variables as $key => $value) {
            $subject = str_replace('{{' . $key . '}}', $value, $subject);
        }

        return $subject;
    }

    /**
     * Get template by event and type
     */
    public static function getTemplate($event, $type, $language = 'en')
    {
        return self::active()
                   ->byEvent($event)
                   ->byType($type)
                   ->byLanguage($language)
                   ->first();
    }

    /**
     * Create default templates
     */
    public static function createDefaults()
    {
        $templates = [
            [
                'name' => 'Student Registration Email',
                'type' => 'email',
                'event' => 'student_registered',
                'subject' => 'Welcome to {{institute_name}}!',
                'content' => 'Dear {{student_name}}, welcome to {{institute_name}}. Your account has been created successfully.',
                'variables' => ['student_name', 'institute_name', 'login_url'],
                'language' => 'en',
            ],
            [
                'name' => 'Document Upload Notification',
                'type' => 'in_app',
                'event' => 'document_uploaded',
                'subject' => 'New Document Uploaded',
                'content' => '{{student_name}} has uploaded a new {{document_type}} document.',
                'variables' => ['student_name', 'document_type', 'document_url'],
                'language' => 'en',
            ],
            [
                'name' => 'Progress Update Email',
                'type' => 'email',
                'event' => 'progress_updated',
                'subject' => 'Application Progress Update',
                'content' => 'Your application progress has been updated to: {{new_stage}}.',
                'variables' => ['student_name', 'old_stage', 'new_stage', 'updated_by'],
                'language' => 'en',
            ],
        ];

        foreach ($templates as $template) {
            self::firstOrCreate(
                ['event' => $template['event'], 'type' => $template['type'], 'language' => $template['language']],
                $template
            );
        }
    }
}

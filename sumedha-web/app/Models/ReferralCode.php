<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class ReferralCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'institute_id',
        'code',
        'type',
        'status',
        'usage_limit',
        'usage_count',
        'expires_at',
        'description',
        'commission_rate',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'expires_at' => 'datetime',
            'commission_rate' => 'decimal:2',
            'usage_count' => 'integer',
            'usage_limit' => 'integer',
        ];
    }

    /**
     * Boot method to generate unique code
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($referralCode) {
            if (empty($referralCode->code)) {
                $referralCode->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function referralTrackings()
    {
        return $this->hasMany(ReferralTracking::class);
    }

    public function students()
    {
        return $this->hasManyThrough(Student::class, ReferralTracking::class, 'referral_code_id', 'id', 'id', 'student_id');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('usage_limit')
                          ->orWhereRaw('usage_count < usage_limit');
                    });
    }

    public function scopeByInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Helper methods
     */
    public function isActive()
    {
        return $this->status === 'active' &&
               ($this->expires_at === null || $this->expires_at->isFuture()) &&
               ($this->usage_limit === null || $this->usage_count < $this->usage_limit);
    }

    public function canBeUsed()
    {
        return $this->isActive();
    }

    public function incrementUsage()
    {
        $this->increment('usage_count');
        
        // Auto-deactivate if usage limit reached
        if ($this->usage_limit && $this->usage_count >= $this->usage_limit) {
            $this->update(['status' => 'exhausted']);
        }
    }

    public function getRemainingUsagesAttribute()
    {
        if ($this->usage_limit === null) {
            return null; // Unlimited
        }
        
        return max(0, $this->usage_limit - $this->usage_count);
    }

    public function getIsExpiredAttribute()
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Generate unique referral code
     */
    public static function generateUniqueCode($length = 8)
    {
        do {
            $code = strtoupper(Str::random($length));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Find active referral code by code string
     */
    public static function findActiveByCode($code)
    {
        return self::where('code', $code)->active()->first();
    }

    /**
     * Create a new referral code for institute
     */
    public static function createForInstitute($instituteId, $data = [])
    {
        $defaults = [
            'institute_id' => $instituteId,
            'type' => 'general',
            'status' => 'active',
            'commission_rate' => Institute::find($instituteId)->commission_rate ?? 10.00,
        ];

        return self::create(array_merge($defaults, $data));
    }
}

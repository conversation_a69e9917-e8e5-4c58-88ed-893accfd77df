<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'admin_id',
        'sender_type',
        'message',
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'message_type',
        'is_read',
        'read_at',
        'is_encrypted',
    ];

    protected function casts(): array
    {
        return [
            'is_read' => 'boolean',
            'is_encrypted' => 'boolean',
            'read_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Scopes
     */
    public function scopeForConversation($query, $studentId, $adminId)
    {
        return $query->where('student_id', $studentId)
                    ->where('admin_id', $adminId);
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeByMessageType($query, $type)
    {
        return $query->where('message_type', $type);
    }

    /**
     * Helper methods
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    public function getFileSizeInMbAttribute()
    {
        return $this->file_size ? round($this->file_size / (1024 * 1024), 2) : null;
    }

    public function getFileUrlAttribute()
    {
        return $this->file_path ? asset('storage/' . $this->file_path) : null;
    }

    public function getSenderNameAttribute()
    {
        if ($this->sender_type === 'student') {
            return $this->student ? $this->student->name : 'Student';
        } else {
            return $this->admin ? $this->admin->name : 'Admin';
        }
    }

    public function isFromStudent()
    {
        return $this->sender_type === 'student';
    }

    public function isFromAdmin()
    {
        return $this->sender_type === 'admin';
    }
}

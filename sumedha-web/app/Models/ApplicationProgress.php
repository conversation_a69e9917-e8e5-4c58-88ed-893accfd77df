<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ApplicationProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'current_stage',
        'stage_history',
        'updated_by',
        'notes',
        'stage_updated_at',
    ];

    protected function casts(): array
    {
        return [
            'stage_history' => 'array',
            'stage_updated_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scopes
     */
    public function scopeByStage($query, $stage)
    {
        return $query->where('current_stage', $stage);
    }

    /**
     * Helper methods
     */
    public function updateStage($newStage, $adminId, $notes = null)
    {
        $history = $this->stage_history ?? [];

        $history[] = [
            'stage' => $newStage,
            'timestamp' => now(),
            'updated_by' => $adminId,
            'notes' => $notes,
            'previous_stage' => $this->current_stage,
        ];

        $this->update([
            'current_stage' => $newStage,
            'stage_history' => $history,
            'updated_by' => $adminId,
            'notes' => $notes,
            'stage_updated_at' => now(),
        ]);
    }

    public function getStageProgressPercentageAttribute()
    {
        $stages = [
            'documents_submitted' => 20,
            'documents_verified' => 40,
            'college_application_sent' => 60,
            'documents_submitted_to_college' => 80,
            'final_process_completed' => 100,
        ];

        return $stages[$this->current_stage] ?? 0;
    }

    public function getStageDisplayNameAttribute()
    {
        $stageNames = [
            'documents_submitted' => 'Documents Submitted',
            'documents_verified' => 'Documents Verified',
            'college_application_sent' => 'College Application Sent',
            'documents_submitted_to_college' => 'Documents Submitted to College',
            'final_process_completed' => 'Final Process Completed',
        ];

        return $stageNames[$this->current_stage] ?? 'Unknown Stage';
    }

    public function getNextStageAttribute()
    {
        $stages = [
            'documents_submitted' => 'documents_verified',
            'documents_verified' => 'college_application_sent',
            'college_application_sent' => 'documents_submitted_to_college',
            'documents_submitted_to_college' => 'final_process_completed',
            'final_process_completed' => null,
        ];

        return $stages[$this->current_stage] ?? null;
    }

    public function canAdvanceToNextStage()
    {
        return $this->next_stage !== null;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ReferralEarning extends Model
{
    use HasFactory;

    protected $fillable = [
        'institute_id',
        'student_id',
        'commission_amount',
        'commission_rate',
        'base_amount',
        'status',
        'payout_method',
        'payout_details',
        'earned_at',
        'paid_at',
        'processed_by',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'commission_amount' => 'decimal:2',
            'commission_rate' => 'decimal:2',
            'base_amount' => 'decimal:2',
            'earned_at' => 'datetime',
            'paid_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function processedBy()
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeByInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeByPayoutMethod($query, $method)
    {
        return $query->where('payout_method', $method);
    }

    /**
     * Helper methods
     */
    public function markAsPaid($processedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now(),
            'processed_by' => $processedBy,
            'notes' => $notes,
        ]);
    }

    public function approve($processedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'approved',
            'processed_by' => $processedBy,
            'notes' => $notes,
        ]);
    }

    public function reject($processedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'rejected',
            'processed_by' => $processedBy,
            'notes' => $notes,
        ]);
    }

    /**
     * Calculate commission based on base amount and rate
     */
    public static function calculateCommission($baseAmount, $commissionRate = 10.00)
    {
        return round(($baseAmount * $commissionRate) / 100, 2);
    }

    /**
     * Create a new referral earning record
     */
    public static function createEarning($instituteId, $studentId, $baseAmount, $commissionRate = null)
    {
        $institute = Institute::find($instituteId);
        $rate = $commissionRate ?? $institute->commission_rate ?? 10.00;
        $commissionAmount = self::calculateCommission($baseAmount, $rate);

        return self::create([
            'institute_id' => $instituteId,
            'student_id' => $studentId,
            'commission_amount' => $commissionAmount,
            'commission_rate' => $rate,
            'base_amount' => $baseAmount,
            'status' => 'pending',
            'earned_at' => now(),
        ]);
    }
}

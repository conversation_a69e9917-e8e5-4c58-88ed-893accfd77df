<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_type',
        'user_id',
        'notification_type',
        'email_enabled',
        'in_app_enabled',
        'sms_enabled',
        'custom_settings',
    ];

    protected function casts(): array
    {
        return [
            'email_enabled' => 'boolean',
            'in_app_enabled' => 'boolean',
            'sms_enabled' => 'boolean',
            'custom_settings' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function user()
    {
        if ($this->user_type === 'admin') {
            return $this->belongsTo(User::class, 'user_id');
        } else {
            return $this->belongsTo(Student::class, 'user_id');
        }
    }

    /**
     * Scopes
     */
    public function scopeForUser($query, $userType, $userId)
    {
        return $query->where('user_type', $userType)->where('user_id', $userId);
    }

    public function scopeByType($query, $notificationType)
    {
        return $query->where('notification_type', $notificationType);
    }

    public function scopeEmailEnabled($query)
    {
        return $query->where('email_enabled', true);
    }

    public function scopeInAppEnabled($query)
    {
        return $query->where('in_app_enabled', true);
    }

    public function scopeSmsEnabled($query)
    {
        return $query->where('sms_enabled', true);
    }

    /**
     * Helper methods
     */
    public function isEnabled($channel)
    {
        switch ($channel) {
            case 'email':
                return $this->email_enabled;
            case 'in_app':
                return $this->in_app_enabled;
            case 'sms':
                return $this->sms_enabled;
            default:
                return false;
        }
    }

    /**
     * Get or create preference for user
     */
    public static function getOrCreateForUser($userType, $userId, $notificationType)
    {
        return self::firstOrCreate(
            [
                'user_type' => $userType,
                'user_id' => $userId,
                'notification_type' => $notificationType,
            ],
            [
                'email_enabled' => true,
                'in_app_enabled' => true,
                'sms_enabled' => false,
            ]
        );
    }

    /**
     * Update preferences for user
     */
    public static function updatePreferences($userType, $userId, $preferences)
    {
        foreach ($preferences as $notificationType => $settings) {
            self::updateOrCreate(
                [
                    'user_type' => $userType,
                    'user_id' => $userId,
                    'notification_type' => $notificationType,
                ],
                $settings
            );
        }
    }

    /**
     * Get all preferences for user
     */
    public static function getPreferencesForUser($userType, $userId)
    {
        return self::forUser($userType, $userId)->get()->keyBy('notification_type');
    }

    /**
     * Check if user should receive notification
     */
    public static function shouldReceive($userType, $userId, $notificationType, $channel)
    {
        $preference = self::forUser($userType, $userId)->byType($notificationType)->first();

        if (!$preference) {
            // Default to enabled if no preference set
            return in_array($channel, ['email', 'in_app']);
        }

        return $preference->isEnabled($channel);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DocumentType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_required',
        'auto_accept',
        'allowed_formats',
        'max_file_size_mb',
        'sort_order',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_required' => 'boolean',
            'auto_accept' => 'boolean',
            'is_active' => 'boolean',
            'allowed_formats' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    public function scopeAutoAccept($query)
    {
        return $query->where('auto_accept', true);
    }

    /**
     * Helper methods
     */
    public function isFormatAllowed($format)
    {
        return in_array(strtolower($format), array_map('strtolower', $this->allowed_formats ?? []));
    }

    public function getMaxFileSizeInBytesAttribute()
    {
        return $this->max_file_size_mb * 1024 * 1024;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Permission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    /**
     * Boot method to generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($permission) {
            if (empty($permission->slug)) {
                $permission->slug = Str::slug($permission->name, '_');
            }
        });
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Create default permissions
     */
    public static function createDefaults()
    {
        $permissions = [
            // User Management
            ['name' => 'Manage Users', 'slug' => 'manage_users', 'category' => 'user_management', 'description' => 'Create, edit, and delete admin users'],
            ['name' => 'Manage Roles', 'slug' => 'manage_roles', 'category' => 'user_management', 'description' => 'Create and manage admin roles and permissions'],

            // Student Management
            ['name' => 'Manage Students', 'slug' => 'manage_students', 'category' => 'student_management', 'description' => 'Create, edit, and manage student records'],
            ['name' => 'View Students', 'slug' => 'view_students', 'category' => 'student_management', 'description' => 'View student information'],

            // Course Management
            ['name' => 'Manage Courses', 'slug' => 'manage_courses', 'category' => 'course_management', 'description' => 'Create, edit, and manage courses'],
            ['name' => 'View Courses', 'slug' => 'view_courses', 'category' => 'course_management', 'description' => 'View course information'],

            // Document Management
            ['name' => 'Manage Documents', 'slug' => 'manage_documents', 'category' => 'document_management', 'description' => 'Upload, review, and manage student documents'],
            ['name' => 'View Documents', 'slug' => 'view_documents', 'category' => 'document_management', 'description' => 'View student documents'],

            // Financial Management
            ['name' => 'Manage Fees', 'slug' => 'manage_fees', 'category' => 'financial_management', 'description' => 'Manage student fees and payments'],
            ['name' => 'View Fees', 'slug' => 'view_fees', 'category' => 'financial_management', 'description' => 'View fee information'],
            ['name' => 'Manage Referrals', 'slug' => 'manage_referrals', 'category' => 'financial_management', 'description' => 'Manage referral programs and earnings'],

            // Communication
            ['name' => 'Manage Notifications', 'slug' => 'manage_notifications', 'category' => 'communication', 'description' => 'Send and manage notifications'],
            ['name' => 'Manage Posts', 'slug' => 'manage_posts', 'category' => 'communication', 'description' => 'Create and manage institute posts'],

            // Analytics & Reporting
            ['name' => 'View Analytics', 'slug' => 'view_analytics', 'category' => 'analytics', 'description' => 'View analytics and reports'],
            ['name' => 'Export Data', 'slug' => 'export_data', 'category' => 'analytics', 'description' => 'Export data and generate reports'],

            // System Settings
            ['name' => 'Manage Settings', 'slug' => 'manage_settings', 'category' => 'system', 'description' => 'Manage system settings and configuration'],
            ['name' => 'Manage Institute', 'slug' => 'manage_institute', 'category' => 'system', 'description' => 'Manage institute information and settings'],
        ];

        foreach ($permissions as $permission) {
            self::firstOrCreate(['slug' => $permission['slug']], $permission);
        }
    }

    /**
     * Get permissions by category
     */
    public static function getByCategory()
    {
        return self::active()->orderBy('category')->orderBy('name')->get()->groupBy('category');
    }

    /**
     * Get permission by slug
     */
    public static function getBySlug($slug)
    {
        return self::where('slug', $slug)->first();
    }
}

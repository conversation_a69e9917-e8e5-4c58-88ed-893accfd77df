<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ReferralTracking extends Model
{
    use HasFactory;

    protected $fillable = [
        'institute_id',
        'referral_code_id',
        'student_id',
        'referral_source',
        'ip_address',
        'user_agent',
        'conversion_status',
        'converted_at',
        'metadata',
    ];

    protected function casts(): array
    {
        return [
            'converted_at' => 'datetime',
            'metadata' => 'array',
        ];
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function referralCode()
    {
        return $this->belongsTo(ReferralCode::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Scopes
     */
    public function scopeConverted($query)
    {
        return $query->where('conversion_status', 'converted');
    }

    public function scopePending($query)
    {
        return $query->where('conversion_status', 'pending');
    }

    public function scopeByInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('referral_source', $source);
    }

    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Helper methods
     */
    public function markAsConverted()
    {
        $this->update([
            'conversion_status' => 'converted',
            'converted_at' => now(),
        ]);
    }

    public function isConverted()
    {
        return $this->conversion_status === 'converted';
    }

    public function getConversionTimeAttribute()
    {
        if (!$this->converted_at) {
            return null;
        }

        return $this->converted_at->diffInHours($this->created_at);
    }

    /**
     * Track a new referral
     */
    public static function trackReferral($instituteId, $referralCodeId, $studentId = null, $data = [])
    {
        $defaults = [
            'institute_id' => $instituteId,
            'referral_code_id' => $referralCodeId,
            'student_id' => $studentId,
            'conversion_status' => $studentId ? 'converted' : 'pending',
            'converted_at' => $studentId ? now() : null,
        ];

        return self::create(array_merge($defaults, $data));
    }

    /**
     * Get conversion rate for a specific period
     */
    public static function getConversionRate($instituteId = null, $startDate = null, $endDate = null)
    {
        $query = self::query();

        if ($instituteId) {
            $query->byInstitute($instituteId);
        }

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        $total = $query->count();
        $converted = $query->converted()->count();

        return $total > 0 ? round(($converted / $total) * 100, 2) : 0;
    }

    /**
     * Get referral analytics
     */
    public static function getAnalytics($instituteId = null, $startDate = null, $endDate = null)
    {
        $query = self::query();

        if ($instituteId) {
            $query->byInstitute($instituteId);
        }

        if ($startDate && $endDate) {
            $query->inDateRange($startDate, $endDate);
        }

        $total = $query->count();
        $converted = $query->converted()->count();
        $pending = $query->pending()->count();

        $sourceBreakdown = $query->selectRaw('referral_source, COUNT(*) as count')
                                ->groupBy('referral_source')
                                ->pluck('count', 'referral_source')
                                ->toArray();

        return [
            'total_referrals' => $total,
            'converted_referrals' => $converted,
            'pending_referrals' => $pending,
            'conversion_rate' => $total > 0 ? round(($converted / $total) * 100, 2) : 0,
            'source_breakdown' => $sourceBreakdown,
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PostLike extends Model
{
    use HasFactory;

    protected $fillable = [
        'post_id',
        'student_id',
    ];

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($like) {
            $like->post->updateLikesCount();
        });

        static::deleted(function ($like) {
            $like->post->updateLikesCount();
        });
    }

    /**
     * Relationships
     */
    public function post()
    {
        return $this->belongsTo(Post::class);
    }

    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Scopes
     */
    public function scopeForPost($query, $postId)
    {
        return $query->where('post_id', $postId);
    }

    public function scopeByStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    /**
     * Helper methods
     */
    public static function toggle($postId, $studentId)
    {
        $like = self::where('post_id', $postId)
                   ->where('student_id', $studentId)
                   ->first();

        if ($like) {
            $like->delete();
            return false; // unliked
        } else {
            self::create([
                'post_id' => $postId,
                'student_id' => $studentId,
            ]);
            return true; // liked
        }
    }

    public static function isLikedBy($postId, $studentId)
    {
        return self::where('post_id', $postId)
                  ->where('student_id', $studentId)
                  ->exists();
    }
}

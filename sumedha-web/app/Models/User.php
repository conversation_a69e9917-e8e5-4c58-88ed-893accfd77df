<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'institute_id',
        'name',
        'email',
        'password',
        'phone',
        'role',
        'user_type',
        'admin_role_id',
        'status',
        'invitation_token',
        'invitation_sent_at',
        'invitation_accepted_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'invitation_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'last_login_at' => 'datetime',
            'invitation_sent_at' => 'datetime',
            'invitation_accepted_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function adminRole()
    {
        return $this->belongsTo(AdminRole::class, 'admin_role_id');
    }

    public function students()
    {
        return $this->hasMany(Student::class, 'institute_id', 'institute_id');
    }

    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class, 'admin_id');
    }

    /**
     * Scopes
     */
    public function scopeForInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByUserType($query, $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * Helper methods
     */
    public function isSuperAdmin()
    {
        return $this->user_type === 'super_admin';
    }

    public function isAdmin()
    {
        return $this->user_type === 'admin';
    }

    public function canManageInstitute()
    {
        return in_array($this->user_type, ['super_admin', 'admin']);
    }

    public function getFullNameAttribute()
    {
        return $this->name;
    }

    /**
     * Permission checking methods
     */
    public function hasPermission($permission)
    {
        // Super admin has all permissions
        if ($this->role === 'super_admin') {
            return true;
        }

        // Check admin role permissions
        if ($this->adminRole) {
            return $this->adminRole->hasPermission($permission);
        }

        // Default permissions based on user_type for backward compatibility
        return $this->hasDefaultPermission($permission);
    }

    public function hasAnyPermission($permissions)
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }

    public function hasAllPermissions($permissions)
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }

    protected function hasDefaultPermission($permission)
    {
        $defaultPermissions = [
            'admin' => ['manage_students', 'manage_courses', 'manage_documents', 'view_analytics'],
            'counselor' => ['manage_students', 'manage_documents'],
            'accountant' => ['manage_fees', 'view_analytics'],
            'teacher' => ['view_students', 'view_courses'],
        ];

        $userPermissions = $defaultPermissions[$this->user_type] ?? [];
        return in_array($permission, $userPermissions);
    }

    public function assignRole($roleSlug)
    {
        $role = AdminRole::getBySlug($roleSlug);
        if ($role) {
            $this->update(['admin_role_id' => $role->id]);
        }
    }

    public function removeRole()
    {
        $this->update(['admin_role_id' => null]);
    }
}

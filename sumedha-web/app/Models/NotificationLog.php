<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'notification_template_id',
        'recipient_type',
        'recipient_id',
        'type',
        'event',
        'subject',
        'content',
        'status',
        'sent_at',
        'delivered_at',
        'read_at',
        'metadata',
        'external_id',
        'error_message',
        'retry_count',
        'next_retry_at',
    ];

    protected function casts(): array
    {
        return [
            'sent_at' => 'datetime',
            'delivered_at' => 'datetime',
            'read_at' => 'datetime',
            'next_retry_at' => 'datetime',
            'metadata' => 'array',
            'retry_count' => 'integer',
        ];
    }

    /**
     * Relationships
     */
    public function notificationTemplate()
    {
        return $this->belongsTo(NotificationTemplate::class);
    }

    public function recipient()
    {
        if ($this->recipient_type === 'admin') {
            return $this->belongsTo(User::class, 'recipient_id');
        } else {
            return $this->belongsTo(Student::class, 'recipient_id');
        }
    }

    /**
     * Scopes
     */
    public function scopeForRecipient($query, $recipientType, $recipientId)
    {
        return $query->where('recipient_type', $recipientType)->where('recipient_id', $recipientId);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByEvent($query, $event)
    {
        return $query->where('event', $event);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Helper methods
     */
    public function markAsSent($externalId = null)
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'external_id' => $externalId,
        ]);
    }

    public function markAsDelivered()
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function markAsRead()
    {
        $this->update([
            'read_at' => now(),
        ]);
    }

    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => now()->addMinutes(pow(2, $this->retry_count)), // Exponential backoff
        ]);
    }

    public function isRead()
    {
        return !is_null($this->read_at);
    }

    public function canRetry()
    {
        return $this->status === 'failed' && $this->retry_count < 3;
    }

    /**
     * Create notification log
     */
    public static function createLog($recipientType, $recipientId, $type, $event, $subject, $content, $templateId = null)
    {
        return self::create([
            'notification_template_id' => $templateId,
            'recipient_type' => $recipientType,
            'recipient_id' => $recipientId,
            'type' => $type,
            'event' => $event,
            'subject' => $subject,
            'content' => $content,
            'status' => 'pending',
        ]);
    }

    /**
     * Get unread count for user
     */
    public static function getUnreadCount($recipientType, $recipientId)
    {
        return self::forRecipient($recipientType, $recipientId)
                   ->byType('in_app')
                   ->unread()
                   ->count();
    }

    /**
     * Get recent notifications for user
     */
    public static function getRecentForUser($recipientType, $recipientId, $limit = 10)
    {
        return self::forRecipient($recipientType, $recipientId)
                   ->byType('in_app')
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }
}

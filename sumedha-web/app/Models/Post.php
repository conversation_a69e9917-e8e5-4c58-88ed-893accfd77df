<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'institute_id',
        'author_id',
        'title',
        'content',
        'image_path',
        'status',
        'published_at',
        'scheduled_at',
        'views_count',
        'likes_count',
        'comments_count',
        'allow_comments',
        'send_notification',
        'tags',
        'category',
        'priority',
        'target_audience',
    ];

    protected function casts(): array
    {
        return [
            'published_at' => 'datetime',
            'scheduled_at' => 'datetime',
            'allow_comments' => 'boolean',
            'send_notification' => 'boolean',
            'tags' => 'array',
            'target_audience' => 'array',
            'views_count' => 'integer',
            'likes_count' => 'integer',
            'comments_count' => 'integer',
            'priority' => 'integer',
        ];
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if ($post->status === 'published' && !$post->published_at) {
                $post->published_at = now();
            }
        });

        static::updating(function ($post) {
            if ($post->isDirty('status') && $post->status === 'published' && !$post->published_at) {
                $post->published_at = now();
            }
        });
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function likes()
    {
        return $this->hasMany(PostLike::class);
    }

    public function comments()
    {
        return $this->hasMany(PostComment::class);
    }

    /**
     * Scopes
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->where('published_at', '<=', now());
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled')
                    ->where('scheduled_at', '>', now());
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeForInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    public function scopeByPriority($query, $priority = null)
    {
        if ($priority) {
            return $query->where('priority', $priority);
        }
        return $query->orderBy('priority', 'desc');
    }

    public function scopeForAudience($query, $audience)
    {
        return $query->where(function($q) use ($audience) {
            $q->whereJsonContains('target_audience', $audience)
              ->orWhereJsonContains('target_audience', 'all');
        });
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('published_at', '>=', Carbon::now()->subDays($days));
    }

    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('views_count', 'desc')
                    ->orderBy('likes_count', 'desc')
                    ->limit($limit);
    }

    /**
     * Helper methods
     */
    public function isPublished()
    {
        return $this->status === 'published' && $this->published_at <= now();
    }

    public function isScheduled()
    {
        return $this->status === 'scheduled' && $this->scheduled_at > now();
    }

    public function isDraft()
    {
        return $this->status === 'draft';
    }

    public function canBePublished()
    {
        return in_array($this->status, ['draft', 'scheduled']);
    }

    public function getExcerpt($length = 150)
    {
        return Str::limit(strip_tags($this->content), $length);
    }

    public function getReadingTime()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $readingTime = ceil($wordCount / 200); // Average reading speed: 200 words per minute
        return max(1, $readingTime);
    }

    public function incrementViews()
    {
        $this->increment('views_count');
    }

    public function updateLikesCount()
    {
        $this->update(['likes_count' => $this->likes()->count()]);
    }

    public function updateCommentsCount()
    {
        $this->update(['comments_count' => $this->comments()->where('is_approved', true)->count()]);
    }

    public function hasTag($tag)
    {
        return in_array($tag, $this->tags ?? []);
    }

    public function addTag($tag)
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->update(['tags' => $tags]);
        }
    }

    public function removeTag($tag)
    {
        $tags = $this->tags ?? [];
        $tags = array_filter($tags, function($t) use ($tag) {
            return $t !== $tag;
        });
        $this->update(['tags' => array_values($tags)]);
    }

    public function isTargetedTo($audience)
    {
        $targetAudience = $this->target_audience ?? [];
        return in_array('all', $targetAudience) || in_array($audience, $targetAudience);
    }

    /**
     * Static methods
     */
    public static function getCategories()
    {
        return [
            'announcement' => 'Announcements',
            'news' => 'News & Updates',
            'event' => 'Events',
            'academic' => 'Academic Information',
            'admission' => 'Admission Updates',
            'scholarship' => 'Scholarships',
            'achievement' => 'Achievements',
            'general' => 'General Information',
        ];
    }

    public static function getPriorities()
    {
        return [
            1 => 'Low',
            2 => 'Normal',
            3 => 'High',
            4 => 'Urgent',
            5 => 'Critical',
        ];
    }

    public static function getTargetAudiences()
    {
        return [
            'all' => 'All Students',
            'new_students' => 'New Students',
            'current_students' => 'Current Students',
            'prospective_students' => 'Prospective Students',
            'alumni' => 'Alumni',
            'parents' => 'Parents',
        ];
    }

    /**
     * Publish the post
     */
    public function publish()
    {
        $this->update([
            'status' => 'published',
            'published_at' => now(),
        ]);
    }

    /**
     * Schedule the post
     */
    public function schedule($dateTime)
    {
        $this->update([
            'status' => 'scheduled',
            'scheduled_at' => $dateTime,
        ]);
    }

    /**
     * Archive the post
     */
    public function archive()
    {
        $this->update(['status' => 'archived']);
    }

    /**
     * Check if post should be auto-published
     */
    public static function publishScheduledPosts()
    {
        $posts = self::where('status', 'scheduled')
                    ->where('scheduled_at', '<=', now())
                    ->get();

        foreach ($posts as $post) {
            $post->publish();
        }

        return $posts->count();
    }
}

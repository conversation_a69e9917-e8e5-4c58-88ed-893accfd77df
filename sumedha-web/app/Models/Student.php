<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Student extends Authenticatable
{
    use HasFactory, Notifiable, HasApiTokens;

    protected $fillable = [
        'institute_id',
        'name',
        'email',
        'phone',
        'address',
        'date_of_birth',
        'gender',
        'nationality',
        'passport_number',
        'password',
        'status',
        'referral_source',
        'registered_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'registered_at' => 'datetime',
        ];
    }

    /**
     * Relationships
     */
    public function institute()
    {
        return $this->belongsTo(Institute::class);
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'student_courses')
                    ->withPivot([
                        'total_course_fee',
                        'documentation_fee',
                        'amount_received',
                        'remaining_fee',
                        'payment_status',
                        'payment_notes',
                        'enrolled_at',
                        'completed_at',
                        'status'
                    ])
                    ->withTimestamps();
    }

    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function applicationProgress()
    {
        return $this->hasOne(ApplicationProgress::class);
    }

    public function referralEarnings()
    {
        return $this->hasMany(ReferralEarning::class);
    }

    public function referralTrackings()
    {
        return $this->hasMany(ReferralTracking::class);
    }

    public function rewardPoints()
    {
        return $this->hasMany(RewardPoint::class);
    }

    public function postLikes()
    {
        return $this->hasMany(PostLike::class);
    }

    public function postComments()
    {
        return $this->hasMany(PostComment::class);
    }

    /**
     * Scopes
     */
    public function scopeForInstitute($query, $instituteId)
    {
        return $query->where('institute_id', $instituteId);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByPaymentStatus($query, $status)
    {
        return $query->whereHas('courses', function ($q) use ($status) {
            $q->where('student_courses.payment_status', $status);
        });
    }

    /**
     * Helper methods
     */
    public function getFullNameAttribute()
    {
        return $this->name;
    }

    public function getTotalFeesAttribute()
    {
        return $this->courses()->sum('student_courses.total_course_fee');
    }

    public function getTotalPaidAttribute()
    {
        return $this->courses()->sum('student_courses.amount_received');
    }

    public function getRemainingFeesAttribute()
    {
        return $this->total_fees - $this->total_paid;
    }

    public function getPaymentStatusAttribute()
    {
        $totalFees = $this->total_fees;
        $totalPaid = $this->total_paid;

        if ($totalPaid == 0) return 'unpaid';
        if ($totalPaid >= $totalFees) return 'fully_paid';
        return 'partially_paid';
    }

    public function getTotalRewardPointsAttribute()
    {
        return RewardPoint::getTotalPoints($this->id, $this->institute_id);
    }

    public function getRewardPointsBreakdownAttribute()
    {
        return RewardPoint::getPointsBreakdown($this->id, $this->institute_id);
    }

    public function awardReferralBonus($referredStudentId, $points = 100)
    {
        return RewardPoint::awardReferralPoints($this->institute_id, $this->id, $referredStudentId, $points);
    }

    public function createReferralEarning($baseAmount, $commissionRate = null)
    {
        return ReferralEarning::createEarning($this->institute_id, $this->id, $baseAmount, $commissionRate);
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Document extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'document_type_id',
        'original_filename',
        'stored_filename',
        'file_path',
        'file_extension',
        'file_size',
        'mime_type',
        'status',
        'admin_comments',
        'verified_by',
        'verified_at',
        'expiry_date',
        'is_expired',
    ];

    protected function casts(): array
    {
        return [
            'verified_at' => 'datetime',
            'expiry_date' => 'date',
            'is_expired' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function student()
    {
        return $this->belongsTo(Student::class);
    }

    public function documentType()
    {
        return $this->belongsTo(DocumentType::class);
    }

    public function verifiedBy()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scopes
     */
    public function scopeForStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePendingVerification($query)
    {
        return $query->where('status', 'pending_verification');
    }

    public function scopeVerified($query)
    {
        return $query->where('status', 'verified');
    }

    public function scopeExpired($query)
    {
        return $query->where('is_expired', true);
    }

    /**
     * Helper methods
     */
    public function getFileSizeInMbAttribute()
    {
        return round($this->file_size / (1024 * 1024), 2);
    }

    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    public function isAutoAcceptable()
    {
        return $this->documentType && $this->documentType->auto_accept;
    }

    public function markAsVerified($adminId, $comments = null)
    {
        $this->update([
            'status' => 'verified',
            'verified_by' => $adminId,
            'verified_at' => now(),
            'admin_comments' => $comments,
        ]);
    }

    public function markAsRejected($adminId, $comments)
    {
        $this->update([
            'status' => 'rejected',
            'verified_by' => $adminId,
            'verified_at' => now(),
            'admin_comments' => $comments,
        ]);
    }
}

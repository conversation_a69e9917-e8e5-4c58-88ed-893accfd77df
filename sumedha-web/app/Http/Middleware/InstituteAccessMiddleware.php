<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class InstituteAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();

        // Super admin has access to everything
        if ($user && $user->user_type === 'super_admin') {
            return $next($request);
        }

        // Regular users must have an institute
        if (!$user || !$user->institute_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. No institute assigned.',
            ], 403);
        }

        // User must be active
        if ($user->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Account is inactive.',
            ], 403);
        }

        return $next($request);
    }
}

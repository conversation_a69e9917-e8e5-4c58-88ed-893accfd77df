<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Student;
use App\Models\Course;
use App\Models\Institute;
use App\Models\AdminRole;
use App\Models\Document;
use App\Models\ChatMessage;
use App\Models\OnlineClass;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        // Remove middleware from constructor - will handle permissions in methods
    }

    /**
     * Display the admin dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Check if user has admin access
        if (!$user->hasPermission('view_analytics')) {
            abort(403, 'Unauthorized access to admin dashboard');
        }

        // Get basic metrics
        $metrics = $this->getDashboardMetrics($user);
        
        // Get recent activities
        $recentActivities = $this->getRecentActivities($user);
        
        // Get chart data for the last 30 days
        $chartData = $this->getChartData($user);

        return response()->json([
            'success' => true,
            'data' => [
                'metrics' => $metrics,
                'recent_activities' => $recentActivities,
                'chart_data' => $chartData,
                'user_permissions' => $this->getUserPermissions($user)
            ]
        ]);
    }

    /**
     * Get dashboard metrics
     */
    private function getDashboardMetrics($user)
    {
        $query = $this->getInstituteQuery($user);
        
        $instituteIds = $query->pluck('institutes.id');

        $metrics = [
            'total_students' => Student::whereIn('institute_id', $instituteIds)->count(),
            'active_courses' => Course::where('status', 'active')->count(),
            'pending_documents' => 0,
            'total_revenue' => 0,
            'monthly_enrollments' => 0,
            'completion_rate' => 0,
        ];

        if ($user->hasPermission('manage_students')) {
            // Pending documents
            $metrics['pending_documents'] = Document::whereIn('student_id', function($q) use ($instituteIds) {
                $q->select('id')->from('students')->whereIn('institute_id', $instituteIds);
            })->where('status', 'pending')->count();

            // Monthly enrollments
            $metrics['monthly_enrollments'] = Student::whereIn('institute_id', $instituteIds)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();
        }

        if ($user->hasPermission('manage_fees')) {
            // Total revenue (from student_courses pivot table)
            $metrics['total_revenue'] = DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->sum('amount_received');

            // Completion rate
            $totalEnrollments = DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->count();

            $completedEnrollments = DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->where('student_courses.status', 'completed')
                ->count();

            $metrics['completion_rate'] = $totalEnrollments > 0 ?
                round(($completedEnrollments / $totalEnrollments) * 100, 2) : 0;
        }

        return $metrics;
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities($user)
    {
        $activities = [];
        $instituteIds = $this->getInstituteQuery($user)->pluck('institutes.id');

        if ($user->hasPermission('manage_students')) {
            // Recent student registrations
            $recentStudents = Student::whereIn('institute_id', $instituteIds)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get(['name', 'created_at']);
                
            foreach ($recentStudents as $student) {
                $activities[] = [
                    'type' => 'student_registration',
                    'message' => "New student registered: {$student->name}",
                    'timestamp' => $student->created_at,
                ];
            }
        }

        if ($user->hasPermission('manage_documents')) {
            // Recent document submissions
            $recentDocuments = Document::whereIn('student_id', function($q) use ($instituteIds) {
                $q->select('id')->from('students')->whereIn('institute_id', $instituteIds);
            })->with('student')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
                
            foreach ($recentDocuments as $document) {
                $activities[] = [
                    'type' => 'document_submission',
                    'message' => "Document submitted by {$document->student->name}: {$document->document_type}",
                    'timestamp' => $document->created_at,
                ];
            }
        }

        // Sort activities by timestamp
        usort($activities, function($a, $b) {
            return $b['timestamp'] <=> $a['timestamp'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Get chart data for analytics
     */
    private function getChartData($user)
    {
        $instituteIds = $this->getInstituteQuery($user)->pluck('institutes.id');
        $last30Days = collect();
        
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $last30Days->push([
                'date' => $date->format('Y-m-d'),
                'label' => $date->format('M d'),
                'students' => 0,
                'revenue' => 0,
            ]);
        }

        if ($user->hasPermission('manage_students')) {
            // Student registrations over time
            $studentData = Student::whereIn('institute_id', $instituteIds)
                ->where('created_at', '>=', Carbon::now()->subDays(30))
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->get()
                ->keyBy('date');

            $last30Days = $last30Days->map(function($day) use ($studentData) {
                $day['students'] = $studentData->get($day['date'])->count ?? 0;
                return $day;
            });
        }

        if ($user->hasPermission('manage_fees')) {
            // Revenue over time
            $revenueData = DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->where('student_courses.updated_at', '>=', Carbon::now()->subDays(30))
                ->selectRaw('DATE(student_courses.updated_at) as date, SUM(amount_received) as revenue')
                ->groupBy('date')
                ->get()
                ->keyBy('date');

            $last30Days = $last30Days->map(function($day) use ($revenueData) {
                $day['revenue'] = $revenueData->get($day['date'])->revenue ?? 0;
                return $day;
            });
        }

        return $last30Days->values();
    }

    /**
     * Get user permissions for frontend
     */
    private function getUserPermissions($user)
    {
        $permissions = [];
        
        $allPermissions = [
            'manage_users', 'manage_roles', 'manage_students', 'manage_courses',
            'manage_documents', 'manage_fees', 'manage_referrals', 'manage_notifications',
            'view_analytics', 'manage_settings', 'export_data', 'manage_posts',
            'manage_classes', 'schedule_classes', 'conduct_classes'
        ];

        foreach ($allPermissions as $permission) {
            $permissions[$permission] = $user->hasPermission($permission);
        }

        return $permissions;
    }

    /**
     * Get institute query based on user permissions
     */
    private function getInstituteQuery($user)
    {
        if ($user->role === 'super_admin') {
            return Institute::query();
        }

        return Institute::where('institutes.id', $user->institute_id);
    }

    /**
     * Get analytics data
     */
    public function analytics(Request $request)
    {
        $user = auth()->user();
        $period = $request->get('period', '30'); // days
        
        $startDate = Carbon::now()->subDays($period);
        $instituteIds = $this->getInstituteQuery($user)->pluck('institutes.id');

        $analytics = [
            'overview' => $this->getDashboardMetrics($user),
            'trends' => $this->getTrendData($instituteIds, $startDate),
            'performance' => $this->getPerformanceData($instituteIds, $startDate),
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    /**
     * Get trend data
     */
    private function getTrendData($instituteIds, $startDate)
    {
        return [
            'student_growth' => Student::whereIn('institute_id', $instituteIds)
                ->where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
                
            'revenue_trend' => DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->where('student_courses.updated_at', '>=', $startDate)
                ->selectRaw('DATE(student_courses.updated_at) as date, SUM(amount_received) as revenue')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
        ];
    }

    /**
     * Get performance data
     */
    private function getPerformanceData($instituteIds, $startDate)
    {
        return [
            'course_popularity' => DB::table('student_courses')
                ->join('students', 'students.id', '=', 'student_courses.student_id')
                ->join('courses', 'courses.id', '=', 'student_courses.course_id')
                ->whereIn('students.institute_id', $instituteIds)
                ->where('student_courses.created_at', '>=', $startDate)
                ->selectRaw('courses.name, COUNT(*) as enrollments')
                ->groupBy('courses.id', 'courses.name')
                ->orderBy('enrollments', 'desc')
                ->get(),

            'document_status' => Document::whereIn('student_id', function($q) use ($instituteIds) {
                $q->select('id')->from('students')->whereIn('institute_id', $instituteIds);
            })->where('created_at', '>=', $startDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->get(),
        ];
    }

    /**
     * User Management Methods
     */

    /**
     * Get all users for management
     */
    public function users(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_users')) {
            abort(403, 'Unauthorized access to user management');
        }

        $query = User::with(['institute', 'adminRole']);

        // Super admin can see all users, others only their institute
        if ($user->role !== 'super_admin') {
            $query->where('institute_id', $user->institute_id);
        }

        // Apply filters
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->has('role')) {
            $query->where('user_type', $request->get('role'));
        }

        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        $users = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $users
        ]);
    }

    /**
     * Create a new user
     */
    public function createUser(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_users')) {
            abort(403, 'Unauthorized to create users');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string|max:20',
            'user_type' => 'required|in:admin,counselor,accountant,teacher',
            'admin_role_id' => 'nullable|exists:admin_roles,id',
            'institute_id' => 'required_unless:user_type,super_admin|exists:institutes,id',
        ]);

        // Generate temporary password
        $tempPassword = 'temp_' . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        $newUser = User::create([
            'institute_id' => $request->institute_id ?? $user->institute_id,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'user_type' => $request->user_type,
            'admin_role_id' => $request->admin_role_id,
            'password' => bcrypt($tempPassword),
            'status' => 'active',
            'invitation_token' => str()->random(32),
            'invitation_sent_at' => now(),
        ]);

        // Assign Spatie role based on user_type
        $newUser->assignRole($request->user_type);

        // Sync admin role permissions if admin_role_id is provided
        if ($request->admin_role_id) {
            $newUser->syncAdminRolePermissions();
        }

        return response()->json([
            'success' => true,
            'message' => 'User created successfully',
            'data' => $newUser->load(['institute', 'adminRole']),
            'temp_password' => $tempPassword
        ], 201);
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, $id)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_users')) {
            abort(403, 'Unauthorized to update users');
        }

        $targetUser = User::findOrFail($id);

        // Check if user can update this target user
        if ($user->role !== 'super_admin' && $targetUser->institute_id !== $user->institute_id) {
            abort(403, 'Cannot update users from other institutes');
        }

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'user_type' => 'sometimes|required|in:admin,counselor,accountant,teacher',
            'admin_role_id' => 'nullable|exists:admin_roles,id',
            'status' => 'sometimes|required|in:active,inactive,suspended',
        ]);

        $targetUser->update($request->only([
            'name', 'email', 'phone', 'user_type', 'admin_role_id', 'status'
        ]));

        // Update Spatie role if user_type changed
        if ($request->has('user_type')) {
            $targetUser->syncRoles([$request->user_type]);
        }

        // Sync admin role permissions if admin_role_id changed
        if ($request->has('admin_role_id')) {
            $targetUser->syncAdminRolePermissions();
        }

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully',
            'data' => $targetUser->load(['institute', 'adminRole'])
        ]);
    }

    /**
     * Delete user
     */
    public function deleteUser($id)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_users')) {
            abort(403, 'Unauthorized to delete users');
        }

        $targetUser = User::findOrFail($id);

        // Check if user can delete this target user
        if ($user->role !== 'super_admin' && $targetUser->institute_id !== $user->institute_id) {
            abort(403, 'Cannot delete users from other institutes');
        }

        // Prevent self-deletion
        if ($targetUser->id === $user->id) {
            abort(400, 'Cannot delete your own account');
        }

        // Prevent deletion of super admin by non-super admin
        if ($targetUser->role === 'super_admin' && $user->role !== 'super_admin') {
            abort(403, 'Cannot delete super admin account');
        }

        $targetUser->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Role Management Methods
     */

    /**
     * Get all admin roles
     */
    public function roles(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_roles')) {
            abort(403, 'Unauthorized access to role management');
        }

        $roles = AdminRole::with(['users' => function($query) use ($user) {
            if ($user->role !== 'super_admin') {
                $query->where('institute_id', $user->institute_id);
            }
        }])->active()->byPriority()->get();

        return response()->json([
            'success' => true,
            'data' => $roles
        ]);
    }

    /**
     * Create a new admin role
     */
    public function createRole(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_roles')) {
            abort(403, 'Unauthorized to create roles');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'required|array',
            'permissions.*' => 'string',
            'priority' => 'nullable|integer|min:0|max:100',
        ]);

        $role = AdminRole::create([
            'name' => $request->name,
            'description' => $request->description,
            'permissions' => $request->permissions,
            'priority' => $request->priority ?? 0,
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Role created successfully',
            'data' => $role
        ], 201);
    }

    /**
     * Update admin role
     */
    public function updateRole(Request $request, $id)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_roles')) {
            abort(403, 'Unauthorized to update roles');
        }

        $role = AdminRole::findOrFail($id);

        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'sometimes|required|array',
            'permissions.*' => 'string',
            'priority' => 'nullable|integer|min:0|max:100',
            'is_active' => 'sometimes|boolean',
        ]);

        $role->update($request->only([
            'name', 'description', 'permissions', 'priority', 'is_active'
        ]));

        // Update slug if name changed
        if ($request->has('name')) {
            $role->update(['slug' => \Illuminate\Support\Str::slug($request->name)]);
        }

        // Sync permissions for all users with this role
        $usersWithRole = User::where('admin_role_id', $role->id)->get();
        foreach ($usersWithRole as $userWithRole) {
            $userWithRole->syncAdminRolePermissions();
        }

        return response()->json([
            'success' => true,
            'message' => 'Role updated successfully',
            'data' => $role
        ]);
    }

    /**
     * Delete admin role
     */
    public function deleteRole($id)
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_roles')) {
            abort(403, 'Unauthorized to delete roles');
        }

        $role = AdminRole::findOrFail($id);

        // Check if role is in use
        $usersCount = User::where('admin_role_id', $role->id)->count();
        if ($usersCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete role. It is assigned to {$usersCount} user(s)."
            ], 400);
        }

        $role->delete();

        return response()->json([
            'success' => true,
            'message' => 'Role deleted successfully'
        ]);
    }

    /**
     * Get available permissions
     */
    public function permissions()
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_roles')) {
            abort(403, 'Unauthorized access to permissions');
        }

        $permissions = [
            'User Management' => [
                'manage_users' => 'Create, edit, and delete users',
                'manage_roles' => 'Create and manage user roles',
                'invite_staff' => 'Send invitations to new staff members',
            ],
            'Student Management' => [
                'manage_students' => 'Full student management access',
                'view_students' => 'View student information',
                'create_students' => 'Add new students',
                'edit_students' => 'Edit student information',
            ],
            'Course Management' => [
                'manage_courses' => 'Full course management access',
                'view_courses' => 'View course information',
                'create_courses' => 'Create new courses',
                'edit_courses' => 'Edit course information',
            ],
            'Document Management' => [
                'manage_documents' => 'Full document management access',
                'view_documents' => 'View student documents',
                'verify_documents' => 'Verify and approve documents',
                'download_documents' => 'Download document files',
            ],
            'Financial Management' => [
                'manage_fees' => 'Full fee management access',
                'view_fees' => 'View fee information',
                'update_fees' => 'Update fee payments',
            ],
            'Communication' => [
                'manage_chat' => 'Full chat management access',
                'view_chat' => 'View chat messages',
                'send_messages' => 'Send chat messages',
                'manage_notifications' => 'Manage notification system',
            ],
            'Analytics & Reports' => [
                'view_analytics' => 'Access analytics dashboard',
                'view_reports' => 'View system reports',
                'export_data' => 'Export data and reports',
            ],
            'Content Management' => [
                'manage_posts' => 'Manage institute posts and announcements',
                'create_posts' => 'Create new posts',
                'edit_posts' => 'Edit existing posts',
                'delete_posts' => 'Delete posts',
            ],
            'Class Management' => [
                'manage_classes' => 'Full online class management',
                'schedule_classes' => 'Schedule new classes',
                'conduct_classes' => 'Conduct online classes',
            ],
            'System' => [
                'manage_settings' => 'Access system settings',
                'manage_institute' => 'Manage institute information',
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $permissions
        ]);
    }
}

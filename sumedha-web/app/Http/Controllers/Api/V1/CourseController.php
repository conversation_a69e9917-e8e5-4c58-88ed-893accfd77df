<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Course;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class CourseController extends BaseController
{
    /**
     * Get all courses
     */
    public function index(Request $request)
    {
        $query = Course::query();

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('level')) {
            $query->where('level', $request->level);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $courses = $query->withCount('students')->paginate($request->get('per_page', 15));

        return $this->sendPaginatedResponse($courses, 'Courses retrieved successfully');
    }

    /**
     * Get course details
     */
    public function show($id)
    {
        $course = Course::with(['students.institute'])->find($id);

        if (!$course) {
            return $this->sendNotFound('Course not found');
        }

        return $this->sendResponse($course, 'Course details retrieved successfully');
    }

    /**
     * Create new course
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:courses,name',
            'description' => 'nullable|string',
            'fee' => 'required|numeric|min:0',
            'duration_months' => 'nullable|integer|min:1',
            'level' => 'nullable|in:N5,N4,N3,N2,N1,Beginner,Intermediate,Advanced',
            'curriculum' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $course = Course::create($request->all());

        return $this->sendResponse($course, 'Course created successfully', 201);
    }

    /**
     * Update course
     */
    public function update(Request $request, $id)
    {
        $course = Course::find($id);

        if (!$course) {
            return $this->sendNotFound('Course not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255|unique:courses,name,' . $id,
            'description' => 'nullable|string',
            'fee' => 'sometimes|required|numeric|min:0',
            'duration_months' => 'nullable|integer|min:1',
            'level' => 'nullable|in:N5,N4,N3,N2,N1,Beginner,Intermediate,Advanced',
            'status' => 'sometimes|in:active,inactive',
            'curriculum' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $course->update($request->all());

        return $this->sendResponse($course, 'Course updated successfully');
    }

    /**
     * Delete course
     */
    public function destroy($id)
    {
        $course = Course::find($id);

        if (!$course) {
            return $this->sendNotFound('Course not found');
        }

        // Check if course has enrolled students
        if ($course->students()->count() > 0) {
            return $this->sendError('Cannot delete course with enrolled students');
        }

        $course->delete();

        return $this->sendResponse([], 'Course deleted successfully');
    }

    /**
     * Assign course to student
     */
    public function assignToStudent(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'course_id' => 'required|exists:courses,id',
            'total_course_fee' => 'required|numeric|min:0',
            'documentation_fee' => 'nullable|numeric|min:0',
            'payment_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student = Student::find($request->student_id);
        $course = Course::find($request->course_id);

        // Check if user has access to this student's institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        // Check if student is already enrolled in this course
        if ($student->courses()->where('course_id', $course->id)->exists()) {
            return $this->sendError('Student is already enrolled in this course');
        }

        $student->courses()->attach($course->id, [
            'total_course_fee' => $request->total_course_fee,
            'documentation_fee' => $request->documentation_fee ?? 0,
            'amount_received' => 0,
            'payment_status' => 'unpaid',
            'payment_notes' => $request->payment_notes,
            'enrolled_at' => now(),
            'status' => 'active',
        ]);

        return $this->sendResponse([
            'student' => $student->load(['courses', 'institute']),
            'course' => $course,
        ], 'Course assigned to student successfully');
    }
}

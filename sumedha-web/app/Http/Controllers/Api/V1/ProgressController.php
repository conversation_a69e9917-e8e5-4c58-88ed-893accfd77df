<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\ApplicationProgress;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ProgressController extends BaseController
{
    /**
     * Get progress overview for institute
     */
    public function getProgressOverview(Request $request)
    {
        $user = auth()->user();

        $query = ApplicationProgress::with(['student.institute']);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->whereHas('student', function ($q) use ($user) {
                $q->where('institute_id', $user->institute_id);
            });
        }

        // Get counts by stage
        $stageCounts = $query->get()->groupBy('current_stage')->map->count();

        $stages = [
            'documents_submitted' => 'Documents Submitted',
            'documents_verified' => 'Documents Verified',
            'college_application_sent' => 'College Application Sent',
            'documents_submitted_to_college' => 'Documents Submitted to College',
            'final_process_completed' => 'Final Process Completed',
        ];

        $overview = [];
        foreach ($stages as $key => $name) {
            $overview[] = [
                'stage' => $key,
                'stage_name' => $name,
                'count' => $stageCounts->get($key, 0),
                'percentage' => $this->getStagePercentage($key),
            ];
        }

        return $this->sendResponse([
            'overview' => $overview,
            'total_students' => $query->count(),
        ], 'Progress overview retrieved successfully');
    }

    /**
     * Get students by progress stage
     */
    public function getStudentsByStage(Request $request, $stage)
    {
        $user = auth()->user();

        $validStages = [
            'documents_submitted',
            'documents_verified',
            'college_application_sent',
            'documents_submitted_to_college',
            'final_process_completed'
        ];

        if (!in_array($stage, $validStages)) {
            return $this->sendError('Invalid stage provided');
        }

        $query = ApplicationProgress::with(['student.institute', 'updatedBy'])
            ->where('current_stage', $stage);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->whereHas('student', function ($q) use ($user) {
                $q->where('institute_id', $user->institute_id);
            });
        }

        $progress = $query->orderBy('stage_updated_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return $this->sendPaginatedResponse($progress, "Students in {$stage} stage retrieved successfully");
    }

    /**
     * Update student progress stage
     */
    public function updateStudentStage(Request $request, $studentId)
    {
        $validator = Validator::make($request->all(), [
            'stage' => 'required|in:documents_submitted,documents_verified,college_application_sent,documents_submitted_to_college,final_process_completed',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student = Student::find($studentId);

        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        $user = auth()->user();

        // Check if user has access to this student's institute
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $progress = ApplicationProgress::where('student_id', $studentId)->first();

        if (!$progress) {
            // Create progress record if it doesn't exist
            $progress = ApplicationProgress::create([
                'student_id' => $studentId,
                'current_stage' => $request->stage,
                'stage_history' => json_encode([
                    [
                        'stage' => $request->stage,
                        'timestamp' => now(),
                        'updated_by' => $user->id,
                        'notes' => $request->notes,
                        'previous_stage' => null,
                    ]
                ]),
                'updated_by' => $user->id,
                'notes' => $request->notes,
                'stage_updated_at' => now(),
            ]);
        } else {
            $progress->updateStage($request->stage, $user->id, $request->notes);
        }

        return $this->sendResponse(
            $progress->load(['student', 'updatedBy']),
            'Student progress updated successfully'
        );
    }

    /**
     * Get student progress history
     */
    public function getStudentProgressHistory($studentId)
    {
        $student = Student::find($studentId);

        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        $user = auth()->user();

        // Check if user has access to this student's institute
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $progress = ApplicationProgress::with(['updatedBy'])
            ->where('student_id', $studentId)
            ->first();

        if (!$progress) {
            return $this->sendNotFound('Progress record not found');
        }

        return $this->sendResponse([
            'student' => $student,
            'current_stage' => $progress->current_stage,
            'stage_display_name' => $progress->stage_display_name,
            'progress_percentage' => $progress->stage_progress_percentage,
            'next_stage' => $progress->next_stage,
            'can_advance' => $progress->canAdvanceToNextStage(),
            'stage_history' => $progress->stage_history,
            'last_updated' => $progress->stage_updated_at,
            'updated_by' => $progress->updatedBy,
            'notes' => $progress->notes,
        ], 'Student progress history retrieved successfully');
    }

    /**
     * Helper method to get stage percentage
     */
    private function getStagePercentage($stage)
    {
        $percentages = [
            'documents_submitted' => 20,
            'documents_verified' => 40,
            'college_application_sent' => 60,
            'documents_submitted_to_college' => 80,
            'final_process_completed' => 100,
        ];

        return $percentages[$stage] ?? 0;
    }
}

<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\NotificationTemplate;
use App\Models\NotificationPreference;
use App\Models\NotificationLog;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends BaseController
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get notifications for current user
     */
    public function getNotifications(Request $request)
    {
        try {
            $user = $request->user();
            $userType = 'admin'; // Since this is admin API
            $limit = $request->get('limit', 20);
            $offset = $request->get('offset', 0);

            $notifications = $this->notificationService->getNotificationsForUser($userType, $user->id, $limit, $offset);
            $unreadCount = $this->notificationService->getUnreadCount($userType, $user->id);

            return $this->sendResponse([
                'notifications' => $notifications,
                'unread_count' => $unreadCount,
                'total_count' => NotificationLog::forRecipient($userType, $user->id)->byType('in_app')->count(),
            ], 'Notifications retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving notifications', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, $id)
    {
        try {
            $user = $request->user();
            $userType = 'admin';

            $result = $this->notificationService->markAsRead($id, $userType, $user->id);

            if ($result) {
                return $this->sendResponse([], 'Notification marked as read');
            } else {
                return $this->sendError('Notification not found or already read');
            }

        } catch (\Exception $e) {
            return $this->sendError('Error marking notification as read', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request)
    {
        try {
            $user = $request->user();
            $userType = 'admin';

            $count = $this->notificationService->markAllAsRead($userType, $user->id);

            return $this->sendResponse(['marked_count' => $count], 'All notifications marked as read');

        } catch (\Exception $e) {
            return $this->sendError('Error marking notifications as read', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Send custom notification
     */
    public function sendNotification(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'recipient_type' => 'required|in:admin,student',
                'recipient_ids' => 'required|array',
                'recipient_ids.*' => 'integer',
                'subject' => 'required|string|max:255',
                'content' => 'required|string',
                'channels' => 'required|array',
                'channels.*' => 'in:email,in_app,sms',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $results = [];
            foreach ($request->recipient_ids as $recipientId) {
                // Create a custom notification log directly
                $log = NotificationLog::createLog(
                    $request->recipient_type,
                    $recipientId,
                    'in_app', // Default to in_app for custom notifications
                    'custom_notification',
                    $request->subject,
                    $request->content
                );

                if (in_array('email', $request->channels)) {
                    // Send email if requested
                    $recipient = $request->recipient_type === 'admin' 
                        ? \App\Models\User::find($recipientId)
                        : \App\Models\Student::find($recipientId);

                    if ($recipient) {
                        $emailLog = NotificationLog::createLog(
                            $request->recipient_type,
                            $recipientId,
                            'email',
                            'custom_notification',
                            $request->subject,
                            $request->content
                        );

                        // Send email using the notification service
                        $this->notificationService->sendNotification(
                            $request->recipient_type,
                            $recipientId,
                            'custom_notification',
                            [],
                            ['email']
                        );
                    }
                }

                $results[] = $recipientId;
            }

            return $this->sendResponse([
                'sent_to' => $results,
                'count' => count($results),
            ], 'Notifications sent successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error sending notifications', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get notification templates
     */
    public function getTemplates(Request $request)
    {
        try {
            $templates = NotificationTemplate::active()
                ->orderBy('event')
                ->orderBy('type')
                ->get();

            return $this->sendResponse($templates, 'Notification templates retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving templates', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Create notification template
     */
    public function createTemplate(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'type' => 'required|in:email,in_app,sms',
                'event' => 'required|string|max:100',
                'subject' => 'nullable|string|max:255',
                'content' => 'required|string',
                'variables' => 'nullable|array',
                'language' => 'nullable|string|size:2',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $template = NotificationTemplate::create([
                'name' => $request->name,
                'type' => $request->type,
                'event' => $request->event,
                'subject' => $request->subject,
                'content' => $request->content,
                'variables' => $request->variables ?? [],
                'language' => $request->language ?? 'en',
                'created_by' => $request->user()->id,
            ]);

            return $this->sendResponse($template, 'Notification template created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error creating template', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Update notification template
     */
    public function updateTemplate(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'subject' => 'nullable|string|max:255',
                'content' => 'sometimes|string',
                'variables' => 'nullable|array',
                'is_active' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $template = NotificationTemplate::findOrFail($id);
            $template->update($request->only(['name', 'subject', 'content', 'variables', 'is_active']));

            return $this->sendResponse($template, 'Notification template updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error updating template', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get notification preferences for current user
     */
    public function getPreferences(Request $request)
    {
        try {
            $user = $request->user();
            $userType = 'admin';

            $preferences = NotificationPreference::getPreferencesForUser($userType, $user->id);

            return $this->sendResponse($preferences, 'Notification preferences retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving preferences', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Update notification preferences
     */
    public function updatePreferences(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'preferences' => 'required|array',
                'preferences.*.email_enabled' => 'boolean',
                'preferences.*.in_app_enabled' => 'boolean',
                'preferences.*.sms_enabled' => 'boolean',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $user = $request->user();
            $userType = 'admin';

            NotificationPreference::updatePreferences($userType, $user->id, $request->preferences);

            return $this->sendResponse([], 'Notification preferences updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error updating preferences', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get notification statistics
     */
    public function getStatistics(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            $startDate = $request->get('start_date', now()->subDays(30));
            $endDate = $request->get('end_date', now());

            $stats = [
                'total_sent' => NotificationLog::whereBetween('created_at', [$startDate, $endDate])->count(),
                'email_sent' => NotificationLog::byType('email')->whereBetween('created_at', [$startDate, $endDate])->count(),
                'in_app_sent' => NotificationLog::byType('in_app')->whereBetween('created_at', [$startDate, $endDate])->count(),
                'failed_notifications' => NotificationLog::failed()->whereBetween('created_at', [$startDate, $endDate])->count(),
                'delivery_rate' => 0,
            ];

            if ($stats['total_sent'] > 0) {
                $delivered = NotificationLog::whereIn('status', ['sent', 'delivered'])
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count();
                $stats['delivery_rate'] = round(($delivered / $stats['total_sent']) * 100, 2);
            }

            // Daily breakdown
            $dailyStats = NotificationLog::selectRaw('DATE(created_at) as date, COUNT(*) as count, type')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date', 'type')
                ->orderBy('date')
                ->get()
                ->groupBy('date');

            return $this->sendResponse([
                'overview' => $stats,
                'daily_breakdown' => $dailyStats,
            ], 'Notification statistics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving statistics', ['error' => $e->getMessage()]);
        }
    }
}

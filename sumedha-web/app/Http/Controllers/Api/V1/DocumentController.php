<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Document;
use App\Models\DocumentType;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class DocumentController extends BaseController
{
    /**
     * Get document types
     */
    public function getDocumentTypes()
    {
        $documentTypes = DocumentType::active()
            ->orderBy('sort_order')
            ->get();

        return $this->sendResponse($documentTypes, 'Document types retrieved successfully');
    }

    /**
     * Get student documents with checklist
     */
    public function getStudentDocuments($studentId)
    {
        $student = Student::find($studentId);
        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        // Check if user has access to this student's institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $documentTypes = DocumentType::active()->orderBy('sort_order')->get();
        $documents = Document::where('student_id', $studentId)
            ->with(['documentType', 'verifiedBy'])
            ->get()
            ->keyBy('document_type_id');

        $checklist = $documentTypes->map(function ($docType) use ($documents) {
            $document = $documents->get($docType->id);

            return [
                'document_type_id' => $docType->id,
                'document_type_name' => $docType->name,
                'description' => $docType->description,
                'is_required' => $docType->is_required,
                'auto_accept' => $docType->auto_accept,
                'status' => $document ? $document->status : 'missing',
                'document' => $document ? [
                    'id' => $document->id,
                    'original_filename' => $document->original_filename,
                    'file_size_mb' => $document->file_size_in_mb,
                    'uploaded_at' => $document->created_at,
                    'verified_at' => $document->verified_at,
                    'verified_by' => $document->verifiedBy ? $document->verifiedBy->name : null,
                    'admin_comments' => $document->admin_comments,
                    'expiry_date' => $document->expiry_date,
                    'is_expired' => $document->is_expired,
                ] : null,
            ];
        });

        return $this->sendResponse([
            'student' => $student->load('institute'),
            'checklist' => $checklist,
            'summary' => [
                'total_required' => $documentTypes->where('is_required', true)->count(),
                'uploaded' => $documents->count(),
                'verified' => $documents->where('status', 'verified')->count(),
                'pending_verification' => $documents->where('status', 'pending_verification')->count(),
                'rejected' => $documents->where('status', 'rejected')->count(),
                'missing' => $documentTypes->where('is_required', true)->count() - $documents->count(),
            ]
        ], 'Student documents retrieved successfully');
    }

    /**
     * Upload document
     */
    public function uploadDocument(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'document_type_id' => 'required|exists:document_types,id',
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student = Student::find($request->student_id);
        $documentType = DocumentType::find($request->document_type_id);

        // Check if user has access to this student's institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        // Validate file type
        $file = $request->file('file');
        $extension = strtolower($file->getClientOriginalExtension());

        if (!$documentType->isFormatAllowed($extension)) {
            return $this->sendError('File format not allowed. Allowed formats: ' . implode(', ', $documentType->allowed_formats));
        }

        // Validate file size
        if ($file->getSize() > $documentType->max_file_size_in_bytes) {
            return $this->sendError('File size exceeds maximum allowed size of ' . $documentType->max_file_size_mb . 'MB');
        }

        // Delete existing document if any
        $existingDocument = Document::where('student_id', $student->id)
            ->where('document_type_id', $documentType->id)
            ->first();

        if ($existingDocument) {
            Storage::delete($existingDocument->file_path);
            $existingDocument->delete();
        }

        // Store file
        $originalFilename = $file->getClientOriginalName();
        $storedFilename = Str::random(40) . '.' . $extension;
        $filePath = $file->storeAs('documents/' . $student->id, $storedFilename, 'private');

        // Create document record
        $document = Document::create([
            'student_id' => $student->id,
            'document_type_id' => $documentType->id,
            'original_filename' => $originalFilename,
            'stored_filename' => $storedFilename,
            'file_path' => $filePath,
            'file_extension' => $extension,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'status' => $documentType->auto_accept ? 'verified' : 'pending_verification',
            'verified_by' => $documentType->auto_accept ? $user->id : null,
            'verified_at' => $documentType->auto_accept ? now() : null,
        ]);

        return $this->sendResponse([
            'document' => $document->load(['documentType', 'verifiedBy']),
            'message' => $documentType->auto_accept ? 'Document uploaded and auto-verified' : 'Document uploaded successfully, pending verification'
        ], 'Document uploaded successfully');
    }

    /**
     * Verify document
     */
    public function verifyDocument(Request $request, $documentId)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:verified,rejected',
            'admin_comments' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $document = Document::with(['student', 'documentType'])->find($documentId);

        if (!$document) {
            return $this->sendNotFound('Document not found');
        }

        // Check if user has access to this document's student institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $document->student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        if ($request->status === 'verified') {
            $document->markAsVerified($user->id, $request->admin_comments);
            $message = 'Document verified successfully';
        } else {
            if (empty($request->admin_comments)) {
                return $this->sendError('Comments are required when rejecting a document');
            }
            $document->markAsRejected($user->id, $request->admin_comments);
            $message = 'Document rejected';
        }

        return $this->sendResponse(
            $document->load(['documentType', 'verifiedBy']),
            $message
        );
    }

    /**
     * Download document
     */
    public function downloadDocument($documentId)
    {
        $document = Document::with(['student'])->find($documentId);

        if (!$document) {
            return $this->sendNotFound('Document not found');
        }

        // Check if user has access to this document's student institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $document->student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        if (!Storage::exists($document->file_path)) {
            return $this->sendNotFound('File not found');
        }

        return Storage::download($document->file_path, $document->original_filename);
    }

    /**
     * Get documents pending verification
     */
    public function getPendingDocuments(Request $request)
    {
        $user = auth()->user();

        $query = Document::with(['student.institute', 'documentType', 'verifiedBy'])
            ->where('status', 'pending_verification');

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->whereHas('student', function ($q) use ($user) {
                $q->where('institute_id', $user->institute_id);
            });
        }

        if ($request->has('document_type_id')) {
            $query->where('document_type_id', $request->document_type_id);
        }

        $documents = $query->orderBy('created_at', 'asc')->paginate($request->get('per_page', 15));

        return $this->sendPaginatedResponse($documents, 'Pending documents retrieved successfully');
    }

    /**
     * Get students with missing documents
     */
    public function getStudentsWithMissingDocuments(Request $request)
    {
        $user = auth()->user();

        $requiredDocTypes = DocumentType::where('is_required', true)->pluck('id');

        $query = Student::with(['institute', 'documents.documentType']);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->where('institute_id', $user->institute_id);
        }

        $allStudents = $query->get();

        // Get students who don't have all required documents
        $studentsWithMissing = [];

        foreach ($allStudents as $student) {
            $uploadedDocTypes = $student->documents->pluck('document_type_id');
            $missingDocTypes = $requiredDocTypes->diff($uploadedDocTypes);

            if ($missingDocTypes->count() > 0) {
                $student->missing_document_types = DocumentType::whereIn('id', $missingDocTypes)->get();
                $student->missing_count = $missingDocTypes->count();
                $studentsWithMissing[] = $student;
            }
        }

        // Convert to collection for pagination
        $students = collect($studentsWithMissing);

        // Manual pagination
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 15);
        $total = $students->count();
        $items = $students->forPage($page, $perPage)->values();

        return $this->sendResponse([
            'data' => $items,
            'pagination' => [
                'current_page' => $page,
                'last_page' => ceil($total / $perPage),
                'per_page' => $perPage,
                'total' => $total,
                'from' => $total > 0 ? ($page - 1) * $perPage + 1 : null,
                'to' => $total > 0 ? min($page * $perPage, $total) : null,
            ]
        ], 'Students with missing documents retrieved successfully');
    }

    /**
     * Request missing document upload
     */
    public function requestMissingDocument(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'document_type_id' => 'required|exists:document_types,id',
            'message' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student = Student::find($request->student_id);
        $documentType = DocumentType::find($request->document_type_id);

        // Check if user has access to this student's institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        // Check if document already exists
        $existingDocument = Document::where('student_id', $student->id)
            ->where('document_type_id', $documentType->id)
            ->first();

        if ($existingDocument) {
            return $this->sendError('Document already uploaded for this type');
        }

        // TODO: Send notification to student (will be implemented in Step 15)
        // For now, we'll just return success

        return $this->sendResponse([
            'student' => $student,
            'document_type' => $documentType,
            'message' => $request->message ?? "Please upload your {$documentType->name}",
        ], 'Document upload request sent to student');
    }
}

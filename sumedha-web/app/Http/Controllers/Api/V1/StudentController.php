<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Student;
use App\Models\Institute;
use App\Models\ApplicationProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class StudentController extends BaseController
{
    /**
     * Get students for institute
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        $query = Student::with(['institute', 'courses', 'applicationProgress']);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->where('institute_id', $user->institute_id);
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('payment_status')) {
            $query->byPaymentStatus($request->payment_status);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $students = $query->paginate($request->get('per_page', 15));

        return $this->sendPaginatedResponse($students, 'Students retrieved successfully');
    }

    /**
     * Get student details
     */
    public function show($id)
    {
        $user = auth()->user();

        $student = Student::with([
            'institute',
            'courses',
            'documents.documentType',
            'applicationProgress',
            'chatMessages' => function ($query) {
                $query->latest()->limit(10);
            }
        ])->find($id);

        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        // Check access
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        return $this->sendResponse($student, 'Student details retrieved successfully');
    }

    /**
     * Create new student
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:students,email',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'nationality' => 'nullable|string|max:100',
            'passport_number' => 'nullable|string|max:50',
            'password' => 'required|string|min:6',
            'referral_source' => 'nullable|in:QR,Link',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $user = auth()->user();

        $student = Student::create([
            'institute_id' => $user->institute_id,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'date_of_birth' => $request->date_of_birth,
            'gender' => $request->gender,
            'nationality' => $request->nationality,
            'passport_number' => $request->passport_number,
            'password' => Hash::make($request->password),
            'referral_source' => $request->referral_source,
            'status' => 'active',
            'registered_at' => now(),
        ]);

        // Create application progress record
        ApplicationProgress::create([
            'student_id' => $student->id,
            'current_stage' => 'documents_submitted',
            'stage_history' => json_encode([
                [
                    'stage' => 'documents_submitted',
                    'timestamp' => now(),
                    'updated_by' => $user->id,
                    'notes' => 'Student registered'
                ]
            ]),
            'updated_by' => $user->id,
            'notes' => 'Student registered and initial progress created',
        ]);

        return $this->sendResponse(
            $student->load(['institute', 'applicationProgress']),
            'Student created successfully',
            201
        );
    }

    /**
     * Update student
     */
    public function update(Request $request, $id)
    {
        $student = Student::find($id);

        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        $user = auth()->user();

        // Check access
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:students,email,' . $id,
            'phone' => 'sometimes|required|string|max:20',
            'address' => 'sometimes|required|string',
            'date_of_birth' => 'nullable|date',
            'gender' => 'nullable|in:male,female,other',
            'nationality' => 'nullable|string|max:100',
            'passport_number' => 'nullable|string|max:50',
            'status' => 'sometimes|in:active,inactive,suspended',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student->update($request->only([
            'name', 'email', 'phone', 'address', 'date_of_birth',
            'gender', 'nationality', 'passport_number', 'status'
        ]));

        return $this->sendResponse(
            $student->load(['institute', 'applicationProgress']),
            'Student updated successfully'
        );
    }
}

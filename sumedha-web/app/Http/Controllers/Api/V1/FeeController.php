<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Student;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class FeeController extends BaseController
{
    /**
     * Get fee overview for institute
     */
    public function getFeeOverview(Request $request)
    {
        $user = auth()->user();

        $query = Student::with(['courses']);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->where('institute_id', $user->institute_id);
        }

        $students = $query->get();

        $totalFees = 0;
        $totalReceived = 0;
        $totalPending = 0;
        $paymentStatusCounts = [
            'unpaid' => 0,
            'partially_paid' => 0,
            'fully_paid' => 0,
        ];

        foreach ($students as $student) {
            foreach ($student->courses as $course) {
                $totalFee = $course->pivot->total_course_fee + $course->pivot->documentation_fee;
                $received = $course->pivot->amount_received;
                $pending = $totalFee - $received;

                $totalFees += $totalFee;
                $totalReceived += $received;
                $totalPending += $pending;

                $paymentStatusCounts[$course->pivot->payment_status]++;
            }
        }

        return $this->sendResponse([
            'overview' => [
                'total_fees' => number_format($totalFees, 2),
                'total_received' => number_format($totalReceived, 2),
                'total_pending' => number_format($totalPending, 2),
                'collection_percentage' => $totalFees > 0 ? round(($totalReceived / $totalFees) * 100, 2) : 0,
            ],
            'payment_status_counts' => $paymentStatusCounts,
            'total_enrollments' => $students->sum(function ($student) {
                return $student->courses->count();
            }),
        ], 'Fee overview retrieved successfully');
    }

    /**
     * Get students with fee details
     */
    public function getStudentFees(Request $request)
    {
        $user = auth()->user();

        $query = Student::with(['institute', 'courses']);

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->where('institute_id', $user->institute_id);
        }

        // Apply filters
        if ($request->has('payment_status')) {
            $query->whereHas('courses', function ($q) use ($request) {
                $q->where('student_courses.payment_status', $request->payment_status);
            });
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $students = $query->paginate($request->get('per_page', 15));

        // Calculate fee details for each student
        $students->getCollection()->transform(function ($student) {
            $totalFees = 0;
            $totalReceived = 0;
            $courseDetails = [];

            foreach ($student->courses as $course) {
                $totalFee = $course->pivot->total_course_fee + $course->pivot->documentation_fee;
                $received = $course->pivot->amount_received;
                $pending = $totalFee - $received;

                $totalFees += $totalFee;
                $totalReceived += $received;

                $courseDetails[] = [
                    'course_id' => $course->id,
                    'course_name' => $course->name,
                    'total_course_fee' => number_format($course->pivot->total_course_fee, 2),
                    'documentation_fee' => number_format($course->pivot->documentation_fee, 2),
                    'total_fee' => number_format($totalFee, 2),
                    'amount_received' => number_format($received, 2),
                    'remaining_fee' => number_format($pending, 2),
                    'payment_status' => $course->pivot->payment_status,
                    'enrolled_at' => $course->pivot->enrolled_at,
                    'payment_notes' => $course->pivot->payment_notes,
                ];
            }

            $student->fee_summary = [
                'total_fees' => number_format($totalFees, 2),
                'total_received' => number_format($totalReceived, 2),
                'total_pending' => number_format($totalFees - $totalReceived, 2),
                'overall_payment_status' => $this->getOverallPaymentStatus($totalFees, $totalReceived),
            ];

            $student->course_fees = $courseDetails;

            return $student;
        });

        return $this->sendPaginatedResponse($students, 'Student fees retrieved successfully');
    }

    /**
     * Update student fee payment
     */
    public function updatePayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'course_id' => 'required|exists:courses,id',
            'amount_received' => 'required|numeric|min:0',
            'payment_notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $student = Student::find($request->student_id);
        $course = Course::find($request->course_id);

        // Check if user has access to this student's institute
        $user = auth()->user();
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        // Check if student is enrolled in this course
        $enrollment = $student->courses()->where('course_id', $course->id)->first();
        if (!$enrollment) {
            return $this->sendError('Student is not enrolled in this course');
        }

        $totalFee = $enrollment->pivot->total_course_fee + $enrollment->pivot->documentation_fee;
        $newAmountReceived = $request->amount_received;

        // Validate amount
        if ($newAmountReceived > $totalFee) {
            return $this->sendError('Amount received cannot exceed total fee');
        }

        // Determine payment status
        $paymentStatus = 'unpaid';
        if ($newAmountReceived > 0 && $newAmountReceived < $totalFee) {
            $paymentStatus = 'partially_paid';
        } elseif ($newAmountReceived >= $totalFee) {
            $paymentStatus = 'fully_paid';
        }

        // Update the pivot table
        $student->courses()->updateExistingPivot($course->id, [
            'amount_received' => $newAmountReceived,
            'payment_status' => $paymentStatus,
            'payment_notes' => $request->payment_notes,
        ]);

        return $this->sendResponse([
            'student' => $student->load(['courses']),
            'course' => $course,
            'payment_details' => [
                'total_fee' => number_format($totalFee, 2),
                'amount_received' => number_format($newAmountReceived, 2),
                'remaining_fee' => number_format($totalFee - $newAmountReceived, 2),
                'payment_status' => $paymentStatus,
            ],
        ], 'Payment updated successfully');
    }

    /**
     * Helper method to determine overall payment status
     */
    private function getOverallPaymentStatus($totalFees, $totalReceived)
    {
        if ($totalReceived == 0) return 'unpaid';
        if ($totalReceived >= $totalFees) return 'fully_paid';
        return 'partially_paid';
    }
}

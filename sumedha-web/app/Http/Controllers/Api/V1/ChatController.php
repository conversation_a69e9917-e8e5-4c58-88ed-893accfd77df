<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\ChatMessage;
use App\Models\Student;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ChatController extends BaseController
{
    /**
     * Get chat conversations for admin
     */
    public function getConversations(Request $request)
    {
        $user = auth()->user();

        $query = ChatMessage::with(['student.institute', 'admin'])
            ->select('student_id', 'admin_id')
            ->selectRaw('MAX(created_at) as last_message_at')
            ->selectRaw('COUNT(CASE WHEN is_read = 0 AND sender_type = "student" THEN 1 END) as unread_count')
            ->groupBy('student_id', 'admin_id');

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->whereHas('student', function ($q) use ($user) {
                $q->where('institute_id', $user->institute_id);
            });
        }

        $conversations = $query->orderBy('last_message_at', 'desc')
            ->paginate($request->get('per_page', 15));

        // Get the latest message for each conversation
        $conversationsWithMessages = $conversations->getCollection()->map(function ($conversation) {
            $latestMessage = ChatMessage::forConversation($conversation->student_id, $conversation->admin_id)
                ->with(['student', 'admin'])
                ->latest()
                ->first();

            $conversation->latest_message = $latestMessage;
            $conversation->student = $latestMessage->student;
            $conversation->admin = $latestMessage->admin;

            return $conversation;
        });

        $conversations->setCollection($conversationsWithMessages);

        return $this->sendPaginatedResponse($conversations, 'Conversations retrieved successfully');
    }

    /**
     * Get messages for a specific conversation
     */
    public function getMessages(Request $request, $studentId)
    {
        $user = auth()->user();

        $student = Student::find($studentId);
        if (!$student) {
            return $this->sendNotFound('Student not found');
        }

        // Check if user has access to this student's institute
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $messages = ChatMessage::forConversation($studentId, $user->id)
            ->with(['student', 'admin'])
            ->orderBy('created_at', 'asc')
            ->paginate($request->get('per_page', 50));

        // Mark admin's unread messages as read
        ChatMessage::forConversation($studentId, $user->id)
            ->where('sender_type', 'student')
            ->unread()
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        return $this->sendPaginatedResponse($messages, 'Messages retrieved successfully');
    }

    /**
     * Send a message
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'message' => 'required_without:file|string|max:2000',
            'file' => 'required_without:message|file|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return $this->sendValidationError($validator->errors());
        }

        $user = auth()->user();
        $student = Student::find($request->student_id);

        // Check if user has access to this student's institute
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        $messageData = [
            'student_id' => $student->id,
            'admin_id' => $user->id,
            'sender_type' => 'admin',
            'message' => $request->message,
            'message_type' => 'text',
            'is_read' => false,
        ];

        // Handle file upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $originalFilename = $file->getClientOriginalName();
            $storedFilename = Str::random(40) . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('chat/' . $student->id, $storedFilename, 'private');

            $messageData = array_merge($messageData, [
                'file_path' => $filePath,
                'file_name' => $originalFilename,
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'message_type' => $this->getMessageTypeFromFile($file),
                'message' => $request->message ?? "Sent a file: {$originalFilename}",
            ]);
        }

        $message = ChatMessage::create($messageData);

        // TODO: Send real-time notification via WebSocket (will be implemented later)
        // TODO: Send push notification to student (will be implemented in Step 15)

        return $this->sendResponse(
            $message->load(['student', 'admin']),
            'Message sent successfully'
        );
    }

    /**
     * Get unread message count for admin
     */
    public function getUnreadCount(Request $request)
    {
        $user = auth()->user();

        $query = ChatMessage::where('sender_type', 'student')
            ->unread()
            ->whereHas('admin', function ($q) use ($user) {
                $q->where('id', $user->id);
            });

        // Filter by institute for non-super admins
        if ($user->user_type !== 'super_admin') {
            $query->whereHas('student', function ($q) use ($user) {
                $q->where('institute_id', $user->institute_id);
            });
        }

        $unreadCount = $query->count();

        return $this->sendResponse([
            'unread_count' => $unreadCount,
        ], 'Unread count retrieved successfully');
    }

    /**
     * Download chat file
     */
    public function downloadFile($messageId)
    {
        $user = auth()->user();

        $message = ChatMessage::with(['student'])->find($messageId);

        if (!$message) {
            return $this->sendNotFound('Message not found');
        }

        // Check if user has access to this message
        if ($user->user_type !== 'super_admin' && $user->institute_id !== $message->student->institute_id) {
            return $this->sendForbidden('Access denied');
        }

        if (!$message->file_path || !Storage::exists($message->file_path)) {
            return $this->sendNotFound('File not found');
        }

        return Storage::download($message->file_path, $message->file_name);
    }

    /**
     * Helper method to determine message type from file
     */
    private function getMessageTypeFromFile($file)
    {
        $mimeType = $file->getMimeType();

        if (str_starts_with($mimeType, 'image/')) {
            return 'image';
        }

        return 'file';
    }
}

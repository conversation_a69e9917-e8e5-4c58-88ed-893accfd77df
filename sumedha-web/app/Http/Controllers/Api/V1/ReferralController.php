<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Api\BaseController;
use App\Models\Institute;
use App\Models\ReferralCode;
use App\Models\ReferralEarning;
use App\Models\ReferralTracking;
use App\Models\RewardPoint;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class ReferralController extends BaseController
{
    /**
     * Get referral overview for institute
     */
    public function getOverview(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $totalStudents = $institute->students()->count();
            $subscribedStudents = $institute->students()->whereHas('courses')->count();
            $totalEarnings = $institute->referralEarnings()->where('status', 'approved')->sum('commission_amount');
            $pendingEarnings = $institute->referralEarnings()->where('status', 'pending')->sum('commission_amount');
            $withdrawableBalance = $institute->referralEarnings()->where('status', 'approved')->sum('commission_amount');
            
            // Get recent referrals (last 30 days)
            $recentReferrals = $institute->referralTrackings()
                ->with(['student', 'referralCode'])
                ->where('created_at', '>=', now()->subDays(30))
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get conversion analytics
            $analytics = ReferralTracking::getAnalytics($institute->id, now()->subDays(30), now());

            return $this->sendResponse([
                'overview' => [
                    'total_students' => $totalStudents,
                    'subscribed_students' => $subscribedStudents,
                    'total_earnings' => $totalEarnings,
                    'pending_earnings' => $pendingEarnings,
                    'withdrawable_balance' => $withdrawableBalance,
                    'conversion_rate' => $analytics['conversion_rate'],
                ],
                'recent_referrals' => $recentReferrals,
                'analytics' => $analytics,
            ], 'Referral overview retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving referral overview', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get referral codes for institute
     */
    public function getReferralCodes(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $codes = $institute->referralCodes()
                ->with(['createdBy'])
                ->orderBy('created_at', 'desc')
                ->paginate(15);

            return $this->sendResponse($codes, 'Referral codes retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving referral codes', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Create new referral code
     */
    public function createReferralCode(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|in:general,campaign,special,limited',
                'description' => 'nullable|string|max:500',
                'usage_limit' => 'nullable|integer|min:1',
                'expires_at' => 'nullable|date|after:now',
                'commission_rate' => 'nullable|numeric|min:0|max:100',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $institute = $request->user()->institute;

            $referralCode = ReferralCode::createForInstitute($institute->id, [
                'type' => $request->type,
                'description' => $request->description,
                'usage_limit' => $request->usage_limit,
                'expires_at' => $request->expires_at,
                'commission_rate' => $request->commission_rate,
                'created_by' => $request->user()->id,
            ]);

            return $this->sendResponse($referralCode, 'Referral code created successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error creating referral code', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Update referral code
     */
    public function updateReferralCode(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'sometimes|in:active,inactive,expired,exhausted',
                'description' => 'nullable|string|max:500',
                'usage_limit' => 'nullable|integer|min:1',
                'expires_at' => 'nullable|date',
                'commission_rate' => 'nullable|numeric|min:0|max:100',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $institute = $request->user()->institute;
            $referralCode = $institute->referralCodes()->findOrFail($id);

            $referralCode->update($request->only([
                'status', 'description', 'usage_limit', 'expires_at', 'commission_rate'
            ]));

            return $this->sendResponse($referralCode, 'Referral code updated successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error updating referral code', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get referral earnings
     */
    public function getEarnings(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $query = $institute->referralEarnings()->with(['student', 'processedBy']);

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('payout_method')) {
                $query->where('payout_method', $request->payout_method);
            }

            if ($request->has('date_from')) {
                $query->where('earned_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->where('earned_at', '<=', $request->date_to);
            }

            $earnings = $query->orderBy('earned_at', 'desc')->paginate(15);

            return $this->sendResponse($earnings, 'Referral earnings retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving referral earnings', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Request payout
     */
    public function requestPayout(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payout_method' => 'required|in:esewa,khalti,bank_transfer',
                'payout_details' => 'required|string|max:500',
                'amount' => 'required|numeric|min:1',
                'earning_ids' => 'required|array',
                'earning_ids.*' => 'exists:referral_earnings,id',
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors());
            }

            $institute = $request->user()->institute;

            DB::beginTransaction();

            // Verify earnings belong to institute and are approved
            $earnings = $institute->referralEarnings()
                ->whereIn('id', $request->earning_ids)
                ->where('status', 'approved')
                ->get();

            if ($earnings->count() !== count($request->earning_ids)) {
                return $this->sendError('Invalid earnings selected for payout');
            }

            $totalAmount = $earnings->sum('commission_amount');

            if ($totalAmount != $request->amount) {
                return $this->sendError('Payout amount does not match selected earnings');
            }

            // Update earnings with payout details
            foreach ($earnings as $earning) {
                $earning->update([
                    'payout_method' => $request->payout_method,
                    'payout_details' => $request->payout_details,
                    'status' => 'paid', // Mark as paid (pending admin approval)
                ]);
            }

            DB::commit();

            return $this->sendResponse([
                'message' => 'Payout request submitted successfully',
                'total_amount' => $totalAmount,
                'earnings_count' => $earnings->count(),
            ], 'Payout request submitted successfully');

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Error processing payout request', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Get referral analytics
     */
    public function getAnalytics(Request $request)
    {
        try {
            $institute = $request->user()->institute;
            
            $startDate = $request->get('start_date', now()->subDays(30));
            $endDate = $request->get('end_date', now());

            $analytics = ReferralTracking::getAnalytics($institute->id, $startDate, $endDate);

            // Additional analytics
            $monthlyEarnings = $institute->referralEarnings()
                ->selectRaw('YEAR(earned_at) as year, MONTH(earned_at) as month, SUM(commission_amount) as total')
                ->where('earned_at', '>=', now()->subMonths(12))
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->get();

            $topPerformingCodes = $institute->referralCodes()
                ->withCount('referralTrackings')
                ->orderBy('referral_trackings_count', 'desc')
                ->limit(5)
                ->get();

            return $this->sendResponse([
                'analytics' => $analytics,
                'monthly_earnings' => $monthlyEarnings,
                'top_performing_codes' => $topPerformingCodes,
            ], 'Referral analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendError('Error retrieving referral analytics', ['error' => $e->getMessage()]);
        }
    }
}
